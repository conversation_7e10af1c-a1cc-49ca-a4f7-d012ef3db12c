# .env.example - Copy this to .env.local and fill in your actual values

# Application Configuration
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_APP_NAME="GhostLayer"

# NextAuth.js Configuration
# Generate a strong secret: openssl rand -base64 32
NEXTAUTH_SECRET=your_super_strong_random_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# Google OAuth Credentials
# Get these from Google Cloud Console
GOOGLE_CLIENT_ID=your_google_client_id_here
GOOGLE_CLIENT_SECRET=your_google_client_secret_here

# Database Configuration
# For SQLite (development)
DATABASE_URL="file:./prisma/dev.db"
# For PostgreSQL (production)
# DATABASE_URL="postgresql://username:password@localhost:5432/stealthwriter_db?schema=public"

# Stripe Configuration (Payment Processing)
NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY=pk_test_your_stripe_publishable_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_stripe_webhook_secret
PREMIUM_PLAN_PRICE_ID=price_your_premium_plan_id

# External API Services
# GPTZero API for AI detection
GPTZERO_API_KEY=your_gptzero_api_key_here

# Hugging Face Inference Providers (Primary humanization service)
# Get your token from: https://huggingface.co/settings/tokens
# Required permissions: "Make calls to Inference Providers"
HUGGINGFACE_API_TOKEN=your_huggingface_api_token_here

# Hugging Face Model Configuration
# Primary model for text humanization - DeepSeek-R1 with DeepThink reasoning capabilities
HF_PRIMARY_MODEL=deepseek-ai/DeepSeek-R1
HF_PRIMARY_PROVIDER=fireworks-ai

# Fallback model hierarchy (Falcon models as secondary, then others)
HF_FALLBACK_MODEL=tiiuae/Falcon3-7B-Instruct
HF_FALLBACK_PROVIDER=huggingface

# Hugging Face API Configuration - Optimized for DeepSeek-R1
HF_API_BASE_URL=https://router.huggingface.co/v1
HF_MAX_TOKENS=4000
HF_TEMPERATURE=0.85
HF_REQUEST_TIMEOUT=45000

# Advanced LLM API Providers (Primary for DeepSeek-R1 with DeepThink)
# Fireworks AI - Primary provider for DeepSeek-R1 with best performance
FIREWORKS_API_KEY=fw-your-fireworks-api-key-here

# Alternative providers for DeepSeek-R1 (fallback options)
NOVITA_API_KEY=your_novita_api_key_here
OPENROUTER_API_KEY=your_openrouter_api_key_here

# DeepSeek-R1 Configuration with DeepThink Reasoning
DEEPSEEK_ENABLE_DEEPTHINK=true
DEEPSEEK_REASONING_DEPTH=high
DEEPSEEK_TARGET_DETECTION=10
DEEPSEEK_MAX_REASONING_TOKENS=1000

# Legacy AI Paraphrasing Services (kept for fallback compatibility)
# OpenAI API (GPT-3.5-turbo for fast paraphrasing)
OPENAI_API_KEY=your_openai_api_key_here

# Groq API (Ultra-fast inference with Llama models)
GROQ_API_KEY=your_groq_api_key_here

# Anthropic API (Claude models - optional backup)
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Legacy Python PEGASUS Service (fallback only)
NEXT_PUBLIC_PARAPHRASE_API_URL=http://localhost:5001/paraphrase
FLASK_PORT=5001

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# Rate Limiting Configuration
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_WINDOW_MS=900000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE_PATH=./logs/app.log

# Feature Flags
NEXT_PUBLIC_ENABLE_ANALYTICS=true
NEXT_PUBLIC_ENABLE_AI_DETECTION=true
NEXT_PUBLIC_ENABLE_PREMIUM_FEATURES=true

# Security Configuration
JWT_SECRET=your_jwt_secret_here
ENCRYPTION_KEY=your_encryption_key_here

# Third-party Integrations
# Google Analytics
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Sentry (Error Monitoring)
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn_here

# Redis (for caching and sessions)
REDIS_URL=redis://localhost:6379
