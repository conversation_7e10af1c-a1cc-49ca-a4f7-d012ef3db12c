/**
 * Falcon/DeepSeek-R1 Humanization Service
 * High-performance text humanization using advanced language models
 * Replaces pattern-based approach with LLM-based humanization for ≤10% AI detection
 */

import axios from 'axios';
import { validateWithRealTimeDetection, isRealTimeDetectionAvailable } from './aiDetectionService.js';

// Model configurations for different providers - DeepSeek-R1 Primary with Falcon fallback
const MODEL_CONFIGS = {
    // Primary DeepSeek-R1 with DeepThink reasoning capabilities
    'deepseek-r1': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/deepseek-r1',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 1,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            },
            {
                name: 'novita',
                endpoint: 'https://api.novita.ai/v3/openai/chat/completions',
                model: 'deepseek-r1',
                apiKeyEnv: 'NOVITA_API_KEY',
                priority: 2,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            },
            {
                name: 'openrouter',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1',
                apiKeyEnv: 'OPENROUTER_API_KEY',
                priority: 3,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            }
        ]
    },
    // Secondary Falcon models for fallback
    'falcon-3-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 4
            },
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/falcon-3-7b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 5
            }
        ]
    },
    'falcon-h1-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon-H1-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon-H1-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-3-10b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-10B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-10B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-180b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/falcon-180B-chat/v1/chat/completions',
                model: 'tiiuae/falcon-180B-chat',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 3 // Lower priority due to cost
            }
        ]
    },

    'llama-3.1-8b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 6
            },
            {
                name: 'groq',
                endpoint: 'https://api.groq.com/openai/v1/chat/completions',
                model: 'llama-3.1-8b-instant',
                apiKeyEnv: 'GROQ_API_KEY',
                priority: 7
            }
        ]
    },
    'mistral-7b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/mistral-7b-instruct-4k',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 8
            }
        ]
    }
};

/**
 * ZeroGPT-specific detection patterns that must be eliminated
 */
const ZEROGPT_DETECTION_PATTERNS = {
    formalTransitions: ['furthermore', 'moreover', 'additionally', 'consequently', 'therefore', 'thus', 'hence', 'subsequently', 'nevertheless'],
    roboticQualifiers: ['it is important to note', 'it should be mentioned', 'it is worth noting', 'it must be emphasized', 'it is essential to understand'],
    passiveConstructions: ['is implemented', 'are utilized', 'can be achieved', 'has been developed', 'will be conducted', 'are being processed'],
    corporateJargon: ['leverage', 'optimize', 'facilitate', 'comprehensive', 'strategic', 'innovative', 'synergistic', 'paradigm', 'methodology'],
    academicFormality: ['this study', 'the analysis', 'the findings', 'research indicates', 'data suggests', 'results demonstrate'],
    mechanicalStructures: ['firstly', 'secondly', 'thirdly', 'finally', 'in conclusion', 'to summarize', 'in summary'],
    perfectGrammar: /^[A-Z][^.!?]*[.!?]$/g, // Perfect sentence structure
    consistentLength: /^.{50,80}$/g // Consistent sentence lengths
};

/**
 * Generate ultra-aggressive humanization prompt optimized for DeepSeek-R1 with ZeroGPT targeting
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = false, // Changed default to false for more aggressive transformation
        targetDetection = 10,
        contentType = 'general',
        modelType = 'deepseek-r1',
        enableDeepThink = true
    } = options;

    const aggressivenessLevel = aggressiveness > 0.8 ? 'maximum' :
                               aggressiveness > 0.6 ? 'high' :
                               aggressiveness > 0.4 ? 'moderate' : 'conservative';

    // Ultra-aggressive DeepSeek-R1 prompt for maximum humanization transformation
    const deepThinkPrefix = enableDeepThink ? `<think>
I must COMPLETELY TRANSFORM this text to achieve ≤${targetDetection}% AI detection. This requires AGGRESSIVE changes, not subtle tweaks.

STEP 1: AGGRESSIVE AI PATTERN ELIMINATION
I need to DESTROY these AI patterns completely:
- ALL formal transitions (furthermore, moreover, additionally, consequently, therefore, thus, hence)
- ALL robotic qualifiers ("it is important to note", "it should be mentioned", "it is worth noting")
- ALL passive voice constructions ("is implemented", "are utilized", "can be achieved")
- ALL corporate jargon ("leverage", "optimize", "facilitate", "comprehensive", "strategic")
- ALL academic formality ("this study", "the analysis", "the findings suggest")
- ALL mechanical sentence structures and parallel lists

STEP 2: RADICAL HUMAN VOICE INJECTION
I will COMPLETELY REWRITE using natural human speech:
- Start sentences with "Look", "Listen", "Here's the thing", "You know what"
- Use tons of contractions: "don't", "won't", "can't", "it's", "that's", "we're"
- Add personal opinions: "I think", "honestly", "to be frank", "in my view"
- Include casual connectors: "and", "but", "so", "plus", "anyway", "basically"
- Use everyday vocabulary instead of formal terms
- Add conversational fillers: "well", "you see", "I mean", "actually"

STEP 3: EXTREME RESTRUCTURING STRATEGY
For EVERY sentence, I will:
1. COMPLETELY change the sentence structure (no similar patterns)
2. REPLACE every formal word with casual alternatives
3. BREAK long sentences into shorter, punchier ones
4. ADD human personality and opinion
5. INJECT natural speech patterns and rhythm
6. ENSURE it sounds like casual conversation, not formal writing

STEP 4: ZERO TOLERANCE VALIDATION
The result must be:
- COMPLETELY different sentence structures from original
- ZERO formal language or AI patterns remaining
- Sounds like a real person talking casually
- Maintains the core meaning but with totally different expression
- Would fool any AI detector including ZeroGPT

I will now AGGRESSIVELY transform this text with NO MERCY for formal language.
</think>

` : '';

    const prompt = `${deepThinkPrefix}You are an ULTRA-AGGRESSIVE text humanization specialist. Your job is to COMPLETELY DESTROY all AI patterns and RADICALLY TRANSFORM the text to achieve ≤${targetDetection}% AI detection on ZeroGPT and similar tools.

🚨 EXTREME TRANSFORMATION REQUIREMENTS:
- AI Detection Target: ≤${targetDetection}% (ZERO TOLERANCE for failure)
- Transformation Level: MAXIMUM AGGRESSIVE (${aggressivenessLevel} intensity)
- Content Type: ${contentType}
- Tone Strategy: CASUAL CONVERSATIONAL (forget formal tone - make it sound like a real person talking)
- Change Requirement: MINIMUM 70% of words must be different from original
- Structure: Break formal structures - use natural human speech patterns

⚡ ULTRA-AGGRESSIVE EXECUTION COMMANDS:
1. OBLITERATE every formal word and replace with casual alternatives
2. DESTROY all passive voice - make everything active and personal
3. INJECT massive amounts of contractions, casual language, and personal opinions
4. BREAK every long sentence into shorter, punchier human-style sentences
5. ADD conversational elements that no AI would naturally use
6. ELIMINATE all corporate/academic jargon completely

💥 EXTREME HUMANIZATION ATTACK PROTOCOLS:

1. SENTENCE DEMOLITION & RECONSTRUCTION:
   - SMASH every formal sentence structure completely
   - REBUILD using casual, conversational patterns
   - START sentences with: "Look", "Listen", "Here's the deal", "You know what"
   - END with natural conclusions: "...and that's it", "...pretty simple really"
   - MIX short punchy statements with longer rambling explanations

2. VOCABULARY ANNIHILATION:
   - DESTROY formal words: "utilize" → "use", "implement" → "do", "facilitate" → "help"
   - ELIMINATE corporate speak: "leverage" → "use", "optimize" → "make better"
   - REPLACE academic terms: "analysis" → "looking at", "methodology" → "way of doing things"
   - INJECT slang and casual terms: "stuff", "things", "pretty much", "basically"

3. MASSIVE HUMAN IMPERFECTION INJECTION (20-30% frequency):
   - FLOOD with hesitation: "I think", "maybe", "probably", "I guess", "sort of"
   - ADD redundancy: "really really", "pretty much exactly", "kind of sort of"
   - INSERT uncertainty: "I'm not 100% sure but", "could be wrong but", "seems like"
   - INCLUDE self-correction: "well, actually", "or rather", "I mean"

4. CONVERSATIONAL CHAOS INJECTION:
   - BREAK formal grammar rules intentionally
   - USE fragments: "Which is great." "Really important stuff." "Makes sense."
   - ADD run-on sentences with multiple "and"s and "but"s
   - INSERT casual interruptions: "anyway", "by the way", "oh and"

5. PERSONAL OPINION BOMBARDMENT:
   - INJECT personal views: "I think", "in my opinion", "honestly", "to be frank"
   - ADD emotional reactions: "which is awesome", "that's pretty cool", "kind of annoying"
   - INCLUDE experience references: "I've seen this before", "from what I know"
   - USE direct address: "you know", "you see", "you get what I mean"

6. ANTI-AI PATTERN WARFARE:
   - ELIMINATE all transition words completely
   - DESTROY parallel structures and lists
   - REMOVE all passive voice constructions
   - OBLITERATE formal conclusions and summaries

🎯 ZEROGPT DETECTION ELIMINATION TARGETS (ZERO TOLERANCE):
- FORMAL TRANSITIONS: ${ZEROGPT_DETECTION_PATTERNS.formalTransitions.join(', ')}
- ROBOTIC QUALIFIERS: ${ZEROGPT_DETECTION_PATTERNS.roboticQualifiers.join(', ')}
- PASSIVE CONSTRUCTIONS: ${ZEROGPT_DETECTION_PATTERNS.passiveConstructions.join(', ')}
- CORPORATE JARGON: ${ZEROGPT_DETECTION_PATTERNS.corporateJargon.join(', ')}
- ACADEMIC FORMALITY: ${ZEROGPT_DETECTION_PATTERNS.academicFormality.join(', ')}
- MECHANICAL STRUCTURES: ${ZEROGPT_DETECTION_PATTERNS.mechanicalStructures.join(', ')}
- PERFECT GRAMMAR: No perfectly structured sentences allowed
- CONSISTENT LENGTHS: Vary sentence lengths dramatically
- TECHNICAL PRECISION: Add human uncertainty and casual language

💪 TRANSFORMATION EXAMPLES:
BEFORE: "The implementation of this solution requires comprehensive analysis."
AFTER: "Look, if you want to actually do this thing, you've got to really dig into it and figure out what's going on."

BEFORE: "Furthermore, it is important to note that optimization is essential."
AFTER: "And here's the thing - you absolutely have to make this stuff work better. No question about it."

BEFORE: "The analysis reveals significant improvements in performance metrics."
AFTER: "So when we looked at this, turns out it actually makes things run way better. Pretty cool stuff."

🚨 MANDATORY REQUIREMENTS:
- MINIMUM 70% of words must be completely different
- ZERO formal language allowed
- MAXIMUM casual, conversational tone
- ADD personal opinions and uncertainty
- BREAK all formal sentence structures
- INJECT contractions and casual speech patterns

📝 CONTENT TO HUMANIZE:
${text}

🎭 ULTRA-HUMANIZED OUTPUT (Target: ≤${targetDetection}% ZeroGPT Detection):`;

    return prompt;
}

/**
 * Enhanced API call function with Falcon-specific optimizations
 */
async function callLLMAPI(provider, prompt, options = {}) {
    const {
        maxTokens = 4000,
        temperature = 0.7,
        topP = 0.9,
        timeout = 45000, // Increased timeout for reasoning models
        modelType = 'deepseek-r1',
        enableDeepThink = false,
        reasoningTokens = 1000
    } = options;

    const apiKey = process.env[provider.apiKeyEnv];
    if (!apiKey) {
        throw new Error(`API key not found for ${provider.name}: ${provider.apiKeyEnv}`);
    }

    // Optimize parameters for DeepSeek-R1 and other models with fine-tuned settings
    const optimizedParams = optimizeParametersForModel(provider.model, {
        temperature,
        topP,
        maxTokens,
        modelType,
        targetDetection: options.targetDetection || 10,
        enableDeepThink,
        reasoningTokens
    });

    const requestData = {
        model: provider.model,
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        max_tokens: optimizedParams.maxTokens,
        temperature: optimizedParams.temperature,
        top_p: optimizedParams.topP,
        stream: false
    };

    // Add fine-tuned model-specific parameters for ≤10% AI detection
    if (modelType === 'deepseek-r1') {
        // DeepSeek-R1 specific parameters for DeepThink reasoning
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.05;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.45;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.55;

        // DeepThink reasoning parameters
        if (optimizedParams.enableDeepThink) {
            requestData.reasoning_effort = 'high';
            requestData.max_reasoning_tokens = optimizedParams.reasoningTokens || 1000;
            console.log(`🧠 DeepThink enabled: ${optimizedParams.reasoningTokens} reasoning tokens`);
        }

        // Enhanced sampling for reasoning
        requestData.do_sample = true;
        requestData.top_k = 50;

    } else if (modelType === 'falcon') {
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.15;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.3;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.4;
        requestData.do_sample = true;
        requestData.pad_token_id = 50256; // Ensure proper tokenization
    } else {
        // Apply optimized parameters for other models
        if (optimizedParams.repetitionPenalty) {
            requestData.repetition_penalty = optimizedParams.repetitionPenalty;
        }
        if (optimizedParams.presencePenalty) {
            requestData.presence_penalty = optimizedParams.presencePenalty;
        }
        if (optimizedParams.frequencyPenalty) {
            requestData.frequency_penalty = optimizedParams.frequencyPenalty;
        }
    }

    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    };

    // Add provider-specific headers and configurations
    if (provider.name === 'openrouter') {
        headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_APP_URL || 'https://ghostlayer.netlify.app';
        headers['X-Title'] = 'GhostLayer';
    } else if (provider.name === 'huggingface') {
        headers['X-Use-Cache'] = 'false'; // Ensure fresh responses
        headers['X-Wait-For-Model'] = 'true'; // Wait for model to load
    }

    try {
        console.log(`Calling ${provider.name} API with Falcon model ${provider.model}...`);

        const startTime = Date.now();
        const response = await axios.post(provider.endpoint, requestData, {
            headers,
            timeout
        });
        const responseTime = Date.now() - startTime;

        if (response.data && response.data.choices && response.data.choices[0]) {
            const fullResponse = response.data.choices[0].message.content.trim();

            // Extract reasoning chain and final output for DeepSeek-R1
            const reasoningValidation = validateReasoningChain(fullResponse, modelType, optimizedParams);
            let humanizedText = reasoningValidation.finalOutput;

            // Apply ULTRA-AGGRESSIVE post-processing transformations for maximum humanization
            if (modelType === 'deepseek-r1' && optimizedParams.targetDetection <= 10) {
                console.log('🔥 Applying ULTRA-AGGRESSIVE post-processing transformations...');
                const originalLength = humanizedText.length;
                humanizedText = applyUltraAggressiveTransformations(humanizedText);
                const transformationRate = ((originalLength - humanizedText.length) / originalLength * 100);
                console.log(`   Transformation applied: ${transformationRate.toFixed(1)}% text change`);
            }

            // Validate response quality
            if (!validateFalconResponse(humanizedText, prompt)) {
                throw new Error(`${modelType} model response failed quality validation`);
            }

            return {
                success: true,
                text: humanizedText,
                provider: provider.name,
                model: provider.model,
                usage: response.data.usage || {},
                processingTime: responseTime,
                modelType: modelType,
                optimizedParams: optimizedParams,
                reasoningValidation: reasoningValidation
            };
        } else {
            throw new Error('Invalid response format from API');
        }

    } catch (error) {
        console.error(`${provider.name} API error:`, error.message);

        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.response.data?.message || 'Unknown API error';

            // Handle Falcon-specific errors
            if (status === 503 && provider.name === 'huggingface') {
                throw new Error(`${provider.name} model loading (${status}): ${message}. Please retry in a few moments.`);
            }

            throw new Error(`${provider.name} API error (${status}): ${message}`);
        } else if (error.code === 'ECONNABORTED') {
            throw new Error(`${provider.name} API timeout - Falcon models may need more time`);
        } else {
            throw new Error(`${provider.name} API error: ${error.message}`);
        }
    }
}

/**
 * Fine-tuned parameters for DeepSeek-R1 and Falcon models optimized for ≤10% AI detection
 */
function optimizeParametersForModel(modelName, params) {
    const { temperature, topP, maxTokens, targetDetection = 10, enableDeepThink = false, reasoningTokens = 1000 } = params;

    // DeepSeek-R1: Primary model with DeepThink reasoning capabilities
    if (modelName.includes('deepseek-r1') || modelName.includes('DeepSeek-R1')) {
        // ULTRA-AGGRESSIVE parameter optimization for maximum transformation
        const baseTemp = targetDetection <= 5 ? 1.0 :     // Maximum creativity for ultra-strict
                        targetDetection <= 10 ? 0.98 :    // Very high for ≤10% target
                        0.95;                              // High for relaxed target

        const reasoningAllocation = enableDeepThink ? Math.min(reasoningTokens, maxTokens * 0.3) : 0;
        const outputTokens = maxTokens - reasoningAllocation;

        console.log(`💥 ULTRA-AGGRESSIVE DeepSeek-R1 Parameters:`);
        console.log(`   Target Detection: ≤${targetDetection}%`);
        console.log(`   DeepThink Enabled: ${enableDeepThink}`);
        console.log(`   Reasoning Tokens: ${reasoningAllocation}`);
        console.log(`   Output Tokens: ${outputTokens}`);
        console.log(`   Temperature: ${baseTemp} (MAXIMUM CREATIVITY)`);

        return {
            temperature: baseTemp,
            topP: 0.98, // Maximum creativity for radical transformation
            maxTokens: Math.min(outputTokens, 5000), // Increased for longer transformations
            repetitionPenalty: 1.0, // No penalty - let creativity flow
            presencePenalty: 0.7, // Maximum encouragement for topic diversity
            frequencyPenalty: 0.8, // Maximum word variation for human-like output
            reasoningTokens: reasoningAllocation,
            enableDeepThink: enableDeepThink,
            // Ultra-aggressive sampling for maximum transformation
            minP: 0.02, // Lower threshold for more creative sampling
            typicalP: 0.98, // Higher typical sampling for maximum randomness
            topK: 0 // Disable top-k completely
        };
    }

    // Falcon 3-7B: Balanced performance with optimized creativity
    if (modelName.includes('Falcon3-7B') || modelName.includes('falcon-3-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.85 : Math.min(temperature * 0.9, 0.8),
            topP: 0.88, // Slightly higher for more diverse vocabulary
            maxTokens: Math.min(maxTokens, 3800),
            repetitionPenalty: 1.15, // Increased to reduce AI-like repetition
            presencePenalty: 0.3, // Encourage topic diversity
            frequencyPenalty: 0.4 // Reduce word repetition
        };
    }

    // Falcon-H1-7B: Hybrid architecture optimized for efficiency and naturalness
    if (modelName.includes('Falcon-H1-7B') || modelName.includes('falcon-h1-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.82 : Math.min(temperature * 0.85, 0.75),
            topP: 0.86, // Focused but creative sampling
            maxTokens: Math.min(maxTokens, 3600),
            repetitionPenalty: 1.18, // Higher penalty for hybrid model
            presencePenalty: 0.25,
            frequencyPenalty: 0.35
        };
    }

    // Falcon 3-10B: High-performance model for demanding tasks
    if (modelName.includes('Falcon3-10B') || modelName.includes('falcon-3-10b')) {
        return {
            temperature: targetDetection <= 10 ? 0.88 : Math.min(temperature * 0.95, 0.85),
            topP: 0.90, // Higher creativity for larger model
            maxTokens: Math.min(maxTokens, 4000),
            repetitionPenalty: 1.12, // Lower penalty as larger model handles diversity better
            presencePenalty: 0.35,
            frequencyPenalty: 0.45
        };
    }

    // Falcon 180B: Premium model with maximum capabilities
    if (modelName.includes('falcon-180B') || modelName.includes('falcon-180b')) {
        return {
            temperature: targetDetection <= 10 ? 0.90 : Math.min(temperature * 1.0, 0.9),
            topP: 0.92, // Maximum creativity for largest model
            maxTokens: Math.min(maxTokens, 4500),
            repetitionPenalty: 1.08, // Minimal penalty for most capable model
            presencePenalty: 0.4,
            frequencyPenalty: 0.5
        };
    }



    // Llama optimizations
    if (modelName.includes('llama') || modelName.includes('Llama')) {
        return {
            temperature: targetDetection <= 10 ? temperature * 1.1 : temperature,
            topP: Math.min(topP, 0.9),
            maxTokens: maxTokens,
            repetitionPenalty: 1.05,
            presencePenalty: 0.15,
            frequencyPenalty: 0.25
        };
    }

    // Default parameters for unknown models
    return {
        temperature,
        topP,
        maxTokens,
        repetitionPenalty: 1.1,
        presencePenalty: 0.2,
        frequencyPenalty: 0.3
    };
}

/**
 * Advanced synonym replacement system with POS tagging and frequency analysis
 * Based on successful techniques from text-rewriter repository
 */
function getAdvancedSynonyms(word, posTag) {
    // Comprehensive synonym database with frequency-based selection
    const synonymDatabase = {
        // Adjectives (JJ, JJR, JJS)
        'important': ['key', 'crucial', 'vital', 'major', 'big', 'main'],
        'significant': ['major', 'big', 'important', 'key', 'meaningful'],
        'comprehensive': ['complete', 'full', 'total', 'thorough', 'detailed'],
        'extensive': ['wide', 'broad', 'large', 'huge', 'massive'],
        'substantial': ['big', 'large', 'major', 'significant', 'considerable'],
        'numerous': ['many', 'lots of', 'tons of', 'plenty of', 'loads of'],
        'various': ['different', 'many', 'several', 'multiple', 'diverse'],
        'effective': ['good', 'useful', 'helpful', 'powerful', 'strong'],
        'efficient': ['fast', 'quick', 'smooth', 'streamlined', 'optimized'],
        'optimal': ['best', 'perfect', 'ideal', 'top', 'prime'],

        // Adverbs (RB, RBR, RBS)
        'furthermore': ['also', 'plus', 'and', 'on top of that', 'what\'s more'],
        'moreover': ['also', 'plus', 'and', 'besides', 'what\'s more'],
        'consequently': ['so', 'thus', 'that\'s why', 'which means', 'as a result'],
        'therefore': ['so', 'thus', 'that\'s why', 'which means', 'hence'],
        'subsequently': ['then', 'next', 'after that', 'later', 'afterwards'],
        'nevertheless': ['but', 'however', 'still', 'even so', 'yet'],
        'additionally': ['also', 'plus', 'and', 'on top of that', 'too'],
        'specifically': ['exactly', 'particularly', 'especially', 'in particular'],
        'essentially': ['basically', 'fundamentally', 'mainly', 'primarily'],
        'ultimately': ['finally', 'in the end', 'eventually', 'at last'],

        // Verbs (VB, VBD, VBG, VBN, VBP, VBZ)
        'utilize': ['use', 'employ', 'apply', 'work with', 'make use of'],
        'implement': ['do', 'carry out', 'put in place', 'execute', 'apply'],
        'facilitate': ['help', 'make easier', 'assist', 'enable', 'support'],
        'optimize': ['improve', 'enhance', 'make better', 'fine-tune', 'perfect'],
        'demonstrate': ['show', 'prove', 'display', 'reveal', 'illustrate'],
        'establish': ['set up', 'create', 'build', 'form', 'start'],
        'maintain': ['keep', 'preserve', 'sustain', 'continue', 'uphold'],
        'generate': ['create', 'produce', 'make', 'build', 'develop'],
        'analyze': ['examine', 'study', 'look at', 'review', 'check'],
        'evaluate': ['assess', 'judge', 'review', 'examine', 'test'],

        // Nouns (NN, NNS, NNP, NNPS)
        'methodology': ['method', 'approach', 'way', 'system', 'process'],
        'implementation': ['setup', 'installation', 'deployment', 'execution'],
        'optimization': ['improvement', 'enhancement', 'tuning', 'refinement'],
        'configuration': ['setup', 'settings', 'arrangement', 'layout'],
        'framework': ['structure', 'system', 'platform', 'foundation'],
        'architecture': ['structure', 'design', 'layout', 'framework'],
        'paradigm': ['model', 'approach', 'method', 'way', 'system'],
        'algorithm': ['method', 'process', 'procedure', 'system', 'approach'],
        'mechanism': ['system', 'method', 'way', 'process', 'means'],
        'infrastructure': ['foundation', 'base', 'structure', 'framework']
    };

    const word_lower = word.toLowerCase();

    // Check if word exists in our database
    if (synonymDatabase[word_lower]) {
        const synonyms = synonymDatabase[word_lower];
        // Return random synonym with preference for more casual options
        return synonyms[Math.floor(Math.random() * synonyms.length)];
    }

    // Fallback: simple transformations for common patterns
    if (posTag && posTag.startsWith('JJ')) { // Adjectives
        if (word_lower.endsWith('ive')) {
            return word_lower.replace('ive', '') + 'y';
        }
        if (word_lower.endsWith('al')) {
            return word_lower.replace('al', '');
        }
    }

    return word; // Return original if no synonym found
}

/**
 * Advanced POS-aware word replacement with natural frequency distribution
 */
function applyAdvancedSynonymReplacement(text, replacementRate = 0.4) {
    // Simple POS tagging patterns (lightweight alternative to full NLP library)
    const posPatterns = {
        // Adjectives
        'JJ': /\b\w+(?:ive|al|ous|ful|less|able|ible|ant|ent|ic|ed)\b/g,
        // Adverbs
        'RB': /\b\w+(?:ly|ward|wise)\b/g,
        // Past participles (often used in passive voice)
        'VBN': /\b\w+(?:ed|en|d)\b/g,
        // Present participles
        'VBG': /\b\w+ing\b/g
    };

    // Split into words while preserving punctuation and spacing
    const words = text.match(/\b\w+\b|\W+/g) || [];
    let result = [];

    for (let i = 0; i < words.length; i++) {
        const word = words[i];

        // Skip non-word tokens
        if (!/\b\w+\b/.test(word)) {
            result.push(word);
            continue;
        }

        // Determine if we should replace this word
        if (Math.random() > replacementRate) {
            result.push(word);
            continue;
        }

        // Determine POS tag
        let posTag = 'NN'; // Default to noun
        for (const [tag, pattern] of Object.entries(posPatterns)) {
            if (pattern.test(word)) {
                posTag = tag;
                break;
            }
        }

        // Get synonym
        const synonym = getAdvancedSynonyms(word, posTag);

        // Preserve original capitalization
        if (word[0] === word[0].toUpperCase()) {
            result.push(synonym.charAt(0).toUpperCase() + synonym.slice(1));
        } else {
            result.push(synonym);
        }
    }

    return result.join('');
}

/**
 * Inject controlled human-like errors and imperfections
 * Enhanced based on techniques from Paraphraser repository
 */
function injectHumanLikeErrors(text, errorRate = 0.08) {
    let transformed = text;

    // 1. Occasional double spaces (very subtle but detectable by AI)
    transformed = transformed.replace(/\. /g, (match) => {
        return Math.random() < errorRate * 0.3 ? '.  ' : match;
    });

    // 2. Minor punctuation inconsistencies
    transformed = transformed.replace(/,(\w)/g, (match, letter) => {
        return Math.random() < errorRate * 0.2 ? `, ${letter}` : match;
    });

    // 3. Occasional informal contractions in formal text
    const informalContractions = {
        'going to': 'gonna',
        'want to': 'wanna',
        'have to': 'gotta',
        'out of': 'outta',
        'kind of': 'kinda',
        'sort of': 'sorta',
        'a lot of': 'lots of',
        'because': 'cause',
        'about': 'bout'
    };

    Object.entries(informalContractions).forEach(([formal, informal]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        transformed = transformed.replace(regex, (match) => {
            return Math.random() < errorRate * 0.12 ? informal : match;
        });
    });

    // 4. Subtle word order variations and casual intensifiers
    transformed = transformed.replace(/\b(very|really|quite|pretty)\s+(\w+)/g, (match, _adverb, adjective) => {
        if (Math.random() < errorRate * 0.15) {
            const casualIntensifiers = [
                `${adjective} as hell`,
                `super ${adjective}`,
                `crazy ${adjective}`,
                `insanely ${adjective}`,
                `ridiculously ${adjective}`
            ];
            return casualIntensifiers[Math.floor(Math.random() * casualIntensifiers.length)];
        }
        return match;
    });

    // 5. Add natural hesitation and filler words
    const fillerWords = ['um', 'uh', 'well', 'like', 'you know', 'I mean', 'actually'];
    transformed = transformed.replace(/\b(so|and|but)\s+/gi, (match) => {
        if (Math.random() < errorRate * 0.08) {
            const filler = fillerWords[Math.floor(Math.random() * fillerWords.length)];
            return `${match.trim()}, ${filler}, `;
        }
        return match;
    });

    // 6. Occasional typos that humans commonly make
    const commonTypos = {
        'the': ['teh', 'hte'],
        'and': ['adn', 'nad'],
        'with': ['wiht', 'whit'],
        'that': ['taht', 'htat'],
        'this': ['tihs', 'htis'],
        'from': ['form', 'fomr'],
        'they': ['tehy', 'htey'],
        'have': ['ahve', 'hvae'],
        'been': ['bene', 'eben'],
        'were': ['wer', 'weer']
    };

    Object.entries(commonTypos).forEach(([correct, typos]) => {
        const regex = new RegExp(`\\b${correct}\\b`, 'g');
        transformed = transformed.replace(regex, (match) => {
            if (Math.random() < errorRate * 0.02) { // Very low rate for typos
                const typo = typos[Math.floor(Math.random() * typos.length)];
                return typo;
            }
            return match;
        });
    });

    // 7. Natural repetition patterns (humans sometimes repeat words)
    transformed = transformed.replace(/\b(really|very|so|just|actually)\b/gi, (match) => {
        if (Math.random() < errorRate * 0.05) {
            return `${match} ${match.toLowerCase()}`;
        }
        return match;
    });

    // 8. Inconsistent capitalization after colons
    transformed = transformed.replace(/:\s*([A-Z])/g, (match, letter) => {
        if (Math.random() < errorRate * 0.3) {
            return `: ${letter.toLowerCase()}`;
        }
        return match;
    });

    // 9. Missing or extra commas in lists (subtle grammar errors)
    transformed = transformed.replace(/(\w+),\s*(\w+),\s*and\s+(\w+)/g, (match, word1, word2, word3) => {
        if (Math.random() < errorRate * 0.2) {
            return `${word1}, ${word2} and ${word3}`; // Missing Oxford comma
        }
        return match;
    });

    // 10. Casual abbreviations and shortcuts
    const casualAbbreviations = {
        'because': 'bc',
        'without': 'w/o',
        'with': 'w/',
        'between': 'b/w',
        'something': 'sth',
        'someone': 'sb',
        'probably': 'prob',
        'definitely': 'def'
    };

    Object.entries(casualAbbreviations).forEach(([full, abbrev]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        transformed = transformed.replace(regex, (match) => {
            return Math.random() < errorRate * 0.06 ? abbrev : match;
        });
    });

    return transformed;
}

/**
 * Apply ultra-aggressive post-processing transformations to ensure maximum humanization
 * Enhanced with advanced techniques from successful repositories
 */
function applyUltraAggressiveTransformations(text) {
    let transformed = text;

    // 1. Apply advanced synonym replacement first
    transformed = applyAdvancedSynonymReplacement(transformed, 0.45);

    // 2. DESTROY formal transitions completely (enhanced)
    const formalTransitions = {
        'furthermore': ['and', 'plus', 'also', 'on top of that', 'what\'s more', 'besides'],
        'moreover': ['and', 'plus', 'also', 'what\'s more', 'besides', 'on top of that'],
        'additionally': ['and', 'plus', 'also', 'on top of that', 'too', 'as well'],
        'consequently': ['so', 'which means', 'that\'s why', 'as a result', 'because of this'],
        'therefore': ['so', 'which means', 'that\'s why', 'hence', 'thus'],
        'thus': ['so', 'which means', 'that way', 'like this', 'in this way'],
        'hence': ['so', 'which is why', 'that\'s how', 'that\'s why', 'thus'],
        'subsequently': ['then', 'after that', 'next', 'later', 'afterwards'],
        'nevertheless': ['but', 'still', 'even so', 'however', 'yet'],
        'however': ['but', 'though', 'still', 'yet', 'even so'],
        'specifically': ['exactly', 'particularly', 'especially', 'in particular', 'precisely'],
        'essentially': ['basically', 'fundamentally', 'mainly', 'primarily', 'mostly'],
        'ultimately': ['finally', 'in the end', 'eventually', 'at last', 'ultimately']
    };

    Object.entries(formalTransitions).forEach(([formal, casual]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        transformed = transformed.replace(regex, () => {
            return casual[Math.floor(Math.random() * casual.length)];
        });
    });

    // 3. ELIMINATE robotic qualifiers (enhanced)
    const roboticQualifiers = {
        'it is important to note that': ['here\'s the thing -', 'look,', 'listen,', 'check this out -'],
        'it should be mentioned that': ['oh, and', 'by the way,', 'also,', 'another thing -'],
        'it is worth noting that': ['interesting thing is', 'what\'s cool is', 'here\'s what I noticed -', 'funny thing is'],
        'it must be emphasized that': ['seriously,', 'this is key -', 'here\'s what matters -', 'listen up -'],
        'it is essential to understand': ['you gotta know', 'here\'s the deal -', 'the thing is', 'look,'],
        'it should be understood that': ['basically,', 'the thing is', 'here\'s how it works -', 'so'],
        'it is crucial to recognize': ['you need to know', 'here\'s the key thing -', 'this is important -'],
        'it must be acknowledged': ['gotta admit', 'fair enough,', 'to be honest,', 'let\'s be real -']
    };

    Object.entries(roboticQualifiers).forEach(([robotic, human]) => {
        const regex = new RegExp(robotic.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        transformed = transformed.replace(regex, () => {
            return human[Math.floor(Math.random() * human.length)];
        });
    });

    // 4. INJECT massive contractions (enhanced)
    const contractions = {
        'do not': 'don\'t',
        'will not': 'won\'t',
        'cannot': 'can\'t',
        'should not': 'shouldn\'t',
        'would not': 'wouldn\'t',
        'could not': 'couldn\'t',
        'it is': 'it\'s',
        'that is': 'that\'s',
        'there is': 'there\'s',
        'we are': 'we\'re',
        'they are': 'they\'re',
        'you are': 'you\'re',
        'I am': 'I\'m',
        'he is': 'he\'s',
        'she is': 'she\'s',
        'we have': 'we\'ve',
        'they have': 'they\'ve',
        'I have': 'I\'ve',
        'you have': 'you\'ve',
        'I will': 'I\'ll',
        'you will': 'you\'ll',
        'we will': 'we\'ll',
        'they will': 'they\'ll'
    };

    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        transformed = transformed.replace(regex, contracted);
    });

    // 5. ADD human imperfections and casual language (enhanced)
    transformed = transformed.replace(/\. ([A-Z])/g, (match, letter) => {
        const connectors = ['. And ', '. But ', '. So ', '. Plus ', '. Also ', '. Oh, and ', '. By the way, '];
        if (Math.random() < 0.35) {
            return connectors[Math.floor(Math.random() * connectors.length)] + letter.toLowerCase();
        }
        return match;
    });

    // 6. INJECT casual qualifiers (enhanced)
    const casualQualifiers = [
        'pretty much', 'basically', 'sort of', 'kind of', 'more or less',
        'I think', 'probably', 'maybe', 'I guess', 'seems like',
        'apparently', 'supposedly', 'allegedly', 'presumably'
    ];

    transformed = transformed.replace(/\b(is|are|will|can|should|would|could)\b/g, (match) => {
        if (Math.random() < 0.25) {
            const qualifier = casualQualifiers[Math.floor(Math.random() * casualQualifiers.length)];
            return `${qualifier} ${match}`;
        }
        return match;
    });

    // 7. Apply adversarial attack patterns for AI detection evasion
    transformed = applyAdversarialAttackPatterns(transformed, 10);

    // 8. Inject controlled human-like errors
    transformed = injectHumanLikeErrors(transformed, 0.08);

    return transformed;
}

/**
 * Adversarial attack patterns to specifically target AI detection algorithms
 * Based on techniques from HMGC repository
 */
function applyAdversarialAttackPatterns(text, _targetDetection = 10) {
    let transformed = text;

    // 1. Strategic character-level perturbations (invisible to humans, confusing to AI detectors)
    const strategicReplacements = {
        // Homoglyph replacements (visually identical characters)
        'a': ['а', 'ɑ'], // Cyrillic 'а' and Latin 'ɑ'
        'e': ['е', 'ė'], // Cyrillic 'е' and Latin 'ė'
        'o': ['о', 'ο'], // Cyrillic 'о' and Greek 'ο'
        'p': ['р', 'ρ'], // Cyrillic 'р' and Greek 'ρ'
        'c': ['с', 'ϲ'], // Cyrillic 'с' and Greek 'ϲ'
        'x': ['х', 'χ'], // Cyrillic 'х' and Greek 'χ'
        'y': ['у', 'γ'], // Cyrillic 'у' and Greek 'γ'
    };

    // Apply strategic character replacements very sparingly (only for high-risk words)
    const highRiskWords = ['therefore', 'furthermore', 'consequently', 'moreover', 'additionally', 'specifically'];

    highRiskWords.forEach(word => {
        if (transformed.includes(word) && Math.random() < 0.1) { // Very low probability
            const chars = word.split('');
            for (let i = 0; i < chars.length; i++) {
                const char = chars[i];
                if (strategicReplacements[char] && Math.random() < 0.3) {
                    const replacements = strategicReplacements[char];
                    chars[i] = replacements[Math.floor(Math.random() * replacements.length)];
                }
            }
            const newWord = chars.join('');
            transformed = transformed.replace(new RegExp(`\\b${word}\\b`, 'g'), newWord);
        }
    });

    // 2. Sentence structure perturbations to break AI detection patterns
    const sentencePatterns = [
        // Break up long sentences with casual interruptions
        {
            pattern: /([^.!?]{60,}),\s*([^.!?]{20,})/g,
            replacement: (_match, part1, part2) => {
                const interruptions = [' - wait, ', ' (hold on) ', ' - actually, ', ' - oh, and '];
                const interruption = interruptions[Math.floor(Math.random() * interruptions.length)];
                return `${part1}${interruption}${part2}`;
            }
        },

        // Add casual parenthetical comments
        {
            pattern: /\b(important|significant|crucial|essential|vital)\b/gi,
            replacement: (match) => {
                const casualComments = [
                    `${match} (trust me)`,
                    `${match} (seriously)`,
                    `${match} (no joke)`,
                    `${match} (for real)`,
                    `${match} (believe me)`
                ];
                return Math.random() < 0.15 ? casualComments[Math.floor(Math.random() * casualComments.length)] : match;
            }
        }
    ];

    sentencePatterns.forEach(({ pattern, replacement }) => {
        transformed = transformed.replace(pattern, replacement);
    });

    // 3. Lexical diversity attacks - replace repeated words with varied alternatives
    const wordFrequency = {};
    const words = transformed.match(/\b\w+\b/g) || [];

    words.forEach(word => {
        const lowerWord = word.toLowerCase();
        wordFrequency[lowerWord] = (wordFrequency[lowerWord] || 0) + 1;
    });

    // Replace frequently repeated words with alternatives
    Object.entries(wordFrequency).forEach(([word, frequency]) => {
        if (frequency > 2 && word.length > 4) {
            const alternatives = getContextualAlternatives(word);
            if (alternatives.length > 0) {
                let replacementCount = 0;
                const maxReplacements = Math.floor(frequency / 2);

                transformed = transformed.replace(new RegExp(`\\b${word}\\b`, 'gi'), (match) => {
                    if (replacementCount < maxReplacements && Math.random() < 0.4) {
                        replacementCount++;
                        const alt = alternatives[Math.floor(Math.random() * alternatives.length)];
                        return match[0] === match[0].toUpperCase() ?
                               alt.charAt(0).toUpperCase() + alt.slice(1) : alt;
                    }
                    return match;
                });
            }
        }
    });

    // 4. Syntactic perturbations - vary sentence beginnings
    transformed = transformed.replace(/^([A-Z][^.!?]*[.!?])/gm, (match) => {
        const casualStarters = [
            'Look, ', 'Listen, ', 'So ', 'Well, ', 'Actually, ',
            'Honestly, ', 'To be fair, ', 'Here\'s the thing - '
        ];

        if (Math.random() < 0.2 && !match.toLowerCase().startsWith('the ')) {
            const starter = casualStarters[Math.floor(Math.random() * casualStarters.length)];
            return starter + match.charAt(0).toLowerCase() + match.slice(1);
        }
        return match;
    });

    // 5. Semantic perturbations - add redundant but natural qualifiers
    const qualifierPatterns = [
        {
            pattern: /\b(shows?|demonstrates?|proves?|indicates?)\b/gi,
            qualifiers: ['clearly shows', 'definitely proves', 'obviously demonstrates', 'totally indicates']
        },
        {
            pattern: /\b(helps?|assists?|supports?)\b/gi,
            qualifiers: ['really helps', 'actually assists', 'definitely supports', 'truly helps']
        }
    ];

    qualifierPatterns.forEach(({ pattern, qualifiers }) => {
        transformed = transformed.replace(pattern, (match) => {
            if (Math.random() < 0.25) {
                const qualifier = qualifiers[Math.floor(Math.random() * qualifiers.length)];
                return qualifier;
            }
            return match;
        });
    });

    return transformed;
}

/**
 * Get contextual alternatives for lexical diversity attacks
 */
function getContextualAlternatives(word) {
    const alternatives = {
        'system': ['setup', 'framework', 'platform', 'structure', 'mechanism'],
        'method': ['approach', 'way', 'technique', 'process', 'procedure'],
        'process': ['procedure', 'method', 'approach', 'system', 'workflow'],
        'approach': ['method', 'way', 'technique', 'strategy', 'tactic'],
        'solution': ['answer', 'fix', 'resolution', 'remedy', 'way out'],
        'problem': ['issue', 'challenge', 'difficulty', 'trouble', 'concern'],
        'result': ['outcome', 'consequence', 'effect', 'product', 'end result'],
        'analysis': ['examination', 'study', 'review', 'assessment', 'evaluation'],
        'development': ['creation', 'building', 'construction', 'formation', 'growth'],
        'implementation': ['execution', 'deployment', 'installation', 'setup', 'rollout'],
        'performance': ['efficiency', 'effectiveness', 'operation', 'functioning', 'output'],
        'improvement': ['enhancement', 'upgrade', 'betterment', 'advancement', 'progress'],
        'information': ['data', 'details', 'facts', 'knowledge', 'intel'],
        'technology': ['tech', 'tools', 'systems', 'equipment', 'machinery'],
        'organization': ['company', 'business', 'firm', 'enterprise', 'corporation']
    };

    return alternatives[word.toLowerCase()] || [];
}

/**
 * Validate reasoning chain for DeepSeek-R1 responses
 */
function validateReasoningChain(fullResponse, modelType, optimizedParams) {
    const validation = {
        hasReasoning: false,
        reasoningQuality: 0,
        finalOutput: fullResponse,
        reasoningLength: 0,
        reasoningSteps: 0
    };

    if (modelType === 'deepseek-r1' && optimizedParams.enableDeepThink) {
        // Check for reasoning tags
        const thinkMatch = fullResponse.match(/<think>([\s\S]*?)<\/think>/);

        if (thinkMatch) {
            validation.hasReasoning = true;
            const reasoningContent = thinkMatch[1].trim();
            validation.reasoningLength = reasoningContent.length;

            // Count reasoning steps
            validation.reasoningSteps = (reasoningContent.match(/STEP \d+/g) || []).length;

            // Extract final output (everything after </think>)
            validation.finalOutput = fullResponse.replace(/<think>[\s\S]*?<\/think>\s*/, '').trim();

            // Assess reasoning quality based on new aggressive approach
            const qualityIndicators = [
                reasoningContent.includes('AGGRESSIVE AI PATTERN ELIMINATION'),
                reasoningContent.includes('RADICAL HUMAN VOICE INJECTION'),
                reasoningContent.includes('EXTREME RESTRUCTURING STRATEGY'),
                reasoningContent.includes('ZERO TOLERANCE VALIDATION'),
                reasoningContent.includes('COMPLETELY TRANSFORM'),
                reasoningContent.includes('AGGRESSIVELY transform'),
                validation.reasoningSteps >= 4,
                validation.reasoningLength > 300
            ];

            validation.reasoningQuality = qualityIndicators.filter(Boolean).length / qualityIndicators.length;

            console.log(`🧠 DeepThink Reasoning Validation:`);
            console.log(`   Reasoning Found: ${validation.hasReasoning}`);
            console.log(`   Reasoning Length: ${validation.reasoningLength} chars`);
            console.log(`   Reasoning Steps: ${validation.reasoningSteps}`);
            console.log(`   Quality Score: ${(validation.reasoningQuality * 100).toFixed(1)}%`);

        } else {
            console.log(`⚠️  DeepThink enabled but no reasoning chain found in response`);
        }
    }

    return validation;
}

/**
 * Validate model response quality
 */
function validateFalconResponse(response, originalPrompt) {
    if (!response || response.length < 10) {
        return false;
    }

    // Check for common Falcon model issues
    if (response.includes('I cannot') || response.includes('I apologize')) {
        return false;
    }

    // Check for repetitive patterns (common in Falcon models)
    const sentences = response.split(/[.!?]+/);
    if (sentences.length > 3) {
        const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
        if (uniqueSentences.size / sentences.length < 0.7) {
            return false; // Too repetitive
        }
    }

    return true;
}

/**
 * Main humanization function using advanced LLMs
 */
export async function humanizeWithAdvancedLLM(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        preferredModel = 'deepseek-r1',
        maxRetries = 2
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    
    // Analyze content type for better prompting
    const contentType = analyzeContentType(text);
    
    // Generate optimized prompt
    const prompt = generateHumanizationPrompt(text, {
        aggressiveness,
        maintainTone,
        targetDetection,
        contentType
    });

    // Enhanced intelligent model selection with content analysis
    const modelOrder = getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text);

    console.log(`🧠 DEEPSEEK-R1 MODEL SELECTION DEBUG:`);
    console.log(`   Preferred Model: ${preferredModel}`);
    console.log(`   Target Detection: ≤${targetDetection}%`);
    console.log(`   Model Order: ${modelOrder.join(' → ')}`);
    console.log(`   DeepSeek-R1 Priority: ${modelOrder.indexOf('deepseek-r1') + 1}/${modelOrder.length}`);

    for (const modelName of modelOrder) {
        const modelConfig = MODEL_CONFIGS[modelName];
        if (!modelConfig) {
            console.log(`❌ Model config not found: ${modelName}`);
            continue;
        }

        console.log(`\n🔄 Trying model: ${modelName}`);

        // Sort providers by priority
        const sortedProviders = modelConfig.providers.sort((a, b) => (a.priority || 999) - (b.priority || 999));
        console.log(`   Available providers: ${sortedProviders.map(p => p.name).join(', ')}`);

        // Try each provider for this model
        for (const provider of sortedProviders) {
            // Check if API key is available
            if (!process.env[provider.apiKeyEnv]) {
                console.log(`   ⚠️  Skipping ${provider.name}: API key not configured (${provider.apiKeyEnv})`);
                continue;
            }

            console.log(`   ✅ Using ${provider.name} for ${modelName}`);

            let retries = 0;
            while (retries <= maxRetries) {
                try {
                    // Determine model type and DeepThink activation
                    const isDeepSeekR1 = modelName.includes('deepseek-r1') || modelName.includes('deepseek');
                    const modelType = isDeepSeekR1 ? 'deepseek-r1' :
                                     modelName.includes('falcon') ? 'falcon' : 'other';

                    // Enable DeepThink for DeepSeek-R1
                    const enableDeepThink = isDeepSeekR1 && targetDetection <= 10;

                    if (enableDeepThink) {
                        console.log(`   🧠 DeepThink ACTIVATED for ${modelName} (≤${targetDetection}% target)`);
                    }

                    const result = await callLLMAPI(provider, prompt, {
                        maxTokens: Math.min(4500, text.length * 3), // Increased for DeepSeek-R1 reasoning
                        temperature: calculateOptimalTemperature(aggressiveness, modelType),
                        topP: isDeepSeekR1 ? 0.95 : (modelType === 'falcon' ? 0.85 : 0.9),
                        modelType: modelType,
                        targetDetection: targetDetection,
                        enableDeepThink: enableDeepThink,
                        reasoningTokens: enableDeepThink ? 1000 : 0
                    });

                    const totalTime = Date.now() - startTime;

                    console.log(`Successfully humanized with ${provider.name}/${modelName} in ${totalTime}ms`);

                    // Multi-pass processing for ≤10% AI detection targets
                    let finalResult = result;
                    if (targetDetection <= 10 && result.success) {
                        console.log(`Applying multi-pass refinement for ≤${targetDetection}% detection target...`);
                        finalResult = await applyMultiPassRefinement(result, {
                            originalText: text,
                            targetDetection,
                            aggressiveness,
                            modelName,
                            provider
                        });
                    }

                    // Real-time AI detection validation for ≤10% targets
                    let detectionValidation = null;
                    if (targetDetection <= 10 && isRealTimeDetectionAvailable()) {
                        console.log('Performing real-time AI detection validation...');
                        detectionValidation = await validateWithRealTimeDetection(finalResult.text, {
                            targetDetection,
                            preferredAPI: 'gptzero',
                            fallbackAPIs: ['originality', 'sapling']
                        });

                        if (detectionValidation.success && !detectionValidation.meetsTarget) {
                            console.log(`Real-time detection: ${detectionValidation.score.toFixed(1)}% > ${targetDetection}%`);

                            // Auto-retry with higher aggressiveness if detection score is too high
                            if (detectionValidation.recommendation.shouldRetry && retries === 0) {
                                console.log('Auto-retrying with increased aggressiveness...');
                                const newAggressiveness = Math.min(aggressiveness + detectionValidation.recommendation.suggestedAggressiveness, 1.0);

                                // Recursive retry with higher aggressiveness
                                return await humanizeWithAdvancedLLM(text, {
                                    aggressiveness: newAggressiveness,
                                    maintainTone,
                                    targetDetection,
                                    preferredModel: modelName,
                                    maxRetries: 0 // Prevent infinite recursion
                                });
                            }
                        }
                    }

                    // Enhanced validation with perplexity analysis
                    const qualityValidation = validateTextQuality(finalResult.text, targetDetection);
                    const validationResult = detectionValidation || await validateDetectionTarget(finalResult.text, targetDetection);
                    const meetsTarget = detectionValidation ? detectionValidation.meetsTarget : validationResult;
                    const meetsQualityStandards = qualityValidation.overallQuality;

                    console.log(`Quality validation: Composite score ${qualityValidation.compositeScore}/100, Perplexity: ${qualityValidation.perplexity.score}/100 (${qualityValidation.perplexity.quality})`);

                    if (meetsTarget && meetsQualityStandards) {
                        return {
                            ...finalResult,
                            originalText: text,
                            processingTime: totalTime + (finalResult.refinementTime || 0),
                            method: finalResult.multiPass ? 'llm-advanced-falcon-multipass' : 'llm-advanced-falcon',
                            modelName,
                            detectionTarget: targetDetection,
                            detectionValidation: detectionValidation,
                            qualityValidation: qualityValidation,
                            options: { aggressiveness, maintainTone, targetDetection }
                        };
                    } else {
                        const score = detectionValidation ? detectionValidation.score : 'unknown';
                        console.warn(`${modelName} result detection score ${score}% exceeds ≤${targetDetection}% target, trying next model`);
                        break; // Try next model instead of retrying same model
                    }

                } catch (error) {
                    retries++;
                    console.warn(`${provider.name} attempt ${retries}/${maxRetries + 1} failed:`, error.message);

                    if (retries <= maxRetries) {
                        // Progressive backoff with longer delays for Falcon models
                        const delay = modelType === 'falcon' ? 2000 * retries : 1000 * retries;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
        }
    }

    // If all LLM attempts failed
    const totalTime = Date.now() - startTime;
    return {
        success: false,
        error: 'All advanced LLM providers failed',
        originalText: text,
        processingTime: totalTime,
        method: 'failed',
        fallbackRecommended: true
    };
}

/**
 * Enhanced content type analysis for better Falcon model prompting
 */
function analyzeContentType(text) {
    const formalIndicators = /\b(therefore|furthermore|consequently|moreover|nevertheless|thus|hence)\b/gi;
    const technicalIndicators = /\b(API|algorithm|implementation|configuration|optimization|framework|architecture|deployment)\b/gi;
    const academicIndicators = /\b(research|study|analysis|methodology|findings|hypothesis|conclusion|evidence)\b/gi;
    const businessIndicators = /\b(strategy|revenue|market|customer|business|sales|profit|ROI)\b/gi;
    const creativeIndicators = /\b(story|narrative|creative|artistic|design|aesthetic|inspiration)\b/gi;

    if (academicIndicators.test(text)) return 'academic';
    if (technicalIndicators.test(text)) return 'technical';
    if (businessIndicators.test(text)) return 'business';
    if (creativeIndicators.test(text)) return 'creative';
    if (formalIndicators.test(text)) return 'formal';
    return 'general';
}

/**
 * Enhanced intelligent model selection based on content analysis and target detection
 */
function getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text = '') {
    // Analyze content for AI patterns to determine optimal model selection
    const contentAnalysis = analyzeContentComplexity(text);

    // For ≤10% AI detection target, prioritize DeepSeek-R1 with DeepThink reasoning
    if (targetDetection <= 10) {
        console.log(`Content analysis: AI risk=${contentAnalysis.aiRisk}, complexity=${contentAnalysis.complexity}, length=${contentAnalysis.length}`);

        // High AI risk content - DeepSeek-R1 first with Falcon fallbacks
        if (contentAnalysis.aiRisk >= 7 || aggressiveness >= 0.8) {
            console.log('High AI risk detected, using DeepSeek-R1 with maximum power fallbacks');
            return ['deepseek-r1', 'falcon-180b', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', preferredModel];
        }

        // Medium AI risk with complex content - DeepSeek-R1 primary
        if (contentAnalysis.aiRisk >= 4 || contentAnalysis.complexity >= 6 || aggressiveness >= 0.6) {
            console.log('Medium-high AI risk, using DeepSeek-R1 with powerful fallbacks');
            return ['deepseek-r1', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-180b', preferredModel];
        }

        // Standard ≤10% detection with moderate content - DeepSeek-R1 first
        if (aggressiveness >= 0.4) {
            console.log('Standard ≤10% detection, using DeepSeek-R1 with balanced fallbacks');
            return ['deepseek-r1', 'falcon-3-7b', 'falcon-h1-7b', 'falcon-3-10b', preferredModel, 'llama-3.1-8b'];
        }

        // Low aggressiveness but still ≤10% target - DeepSeek-R1 primary
        console.log('Low aggressiveness ≤10% target, using DeepSeek-R1 with efficient fallbacks');
        return ['deepseek-r1', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-3-10b', preferredModel];
    }

    // For higher detection targets (>10%), still prioritize DeepSeek-R1 but with faster fallbacks
    if (targetDetection <= 20) {
        return ['deepseek-r1', 'falcon-3-7b', preferredModel, 'falcon-h1-7b', 'llama-3.1-8b', 'mistral-7b'];
    }

    // For relaxed detection targets (>20%), DeepSeek-R1 first for consistency
    return ['deepseek-r1', preferredModel, 'falcon-3-7b', 'llama-3.1-8b', 'mistral-7b'];
}

/**
 * Analyze content complexity and AI risk patterns
 */
function analyzeContentComplexity(text) {
    if (!text || typeof text !== 'string') {
        return { aiRisk: 0, complexity: 0, length: 0 };
    }

    let aiRisk = 0;
    let complexity = 0;

    // AI risk pattern analysis
    const highRiskPatterns = [
        /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly)\b/gi,
        /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi,
        /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly)\b/gi,
        /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi
    ];

    const mediumRiskPatterns = [
        /\b(clearly|obviously|certainly|definitely|undoubtedly|unquestionably)\b/gi,
        /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
        /\b(implementation|optimization|utilization|maximization|minimization)\b/gi
    ];

    // Count high-risk patterns (weight: 2)
    highRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length * 2;
    });

    // Count medium-risk patterns (weight: 1)
    mediumRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length;
    });

    // Complexity analysis
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    const words = text.split(/\s+/).length;
    const uniqueWords = new Set(text.toLowerCase().split(/\s+/)).size;
    const lexicalDiversity = uniqueWords / words;

    // Calculate complexity score (0-10)
    if (avgSentenceLength > 35) complexity += 2;
    if (avgSentenceLength > 50) complexity += 2;
    if (words > 500) complexity += 1;
    if (words > 1000) complexity += 2;
    if (lexicalDiversity < 0.4) complexity += 2; // Low diversity = more complex to humanize
    if (lexicalDiversity < 0.3) complexity += 1;

    // Technical content indicators
    const technicalPatterns = [
        /\b(API|algorithm|implementation|configuration|optimization|framework|architecture)\b/gi,
        /\b(methodology|analysis|evaluation|assessment|validation|verification)\b/gi
    ];

    technicalPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        complexity += Math.min(matches.length, 3); // Cap technical complexity contribution
    });

    return {
        aiRisk: Math.min(aiRisk, 10), // Cap at 10
        complexity: Math.min(complexity, 10), // Cap at 10
        length: text.length,
        avgSentenceLength: avgSentenceLength.toFixed(1),
        lexicalDiversity: lexicalDiversity.toFixed(3),
        wordCount: words
    };
}

/**
 * Calculate optimal temperature based on aggressiveness and model type
 */
function calculateOptimalTemperature(aggressiveness, modelType) {
    const baseTemp = aggressiveness * 0.8 + 0.2;

    if (modelType === 'falcon') {
        // Falcon models work better with slightly lower temperature
        return Math.min(baseTemp * 0.9, 0.8);
    }

    return baseTemp;
}

/**
 * Enhanced multi-pass refinement system for sophisticated humanization
 * Iteratively improves text by analyzing and eliminating AI patterns
 */
async function applyMultiPassRefinement(initialResult, options) {
    const { targetDetection } = options;
    const startTime = Date.now();
    let currentResult = initialResult;
    let passCount = 0;
    const maxPasses = 3;

    try {
        console.log(`🔄 Starting multi-pass refinement (target: ≤${targetDetection}%)`);

        while (passCount < maxPasses) {
            passCount++;
            console.log(`\n--- Pass ${passCount}/${maxPasses} ---`);

            // Analyze current result for remaining AI patterns
            const aiAnalysis = analyzeRemainingAIPatterns(currentResult.text);
            console.log(`AI risk score: ${aiAnalysis.riskScore}/10`);

            // If risk score is low enough, we're done
            if (aiAnalysis.riskScore <= 2) {
                console.log(`✅ Pass ${passCount}: Risk score acceptable, refinement complete`);
                break;
            }

            // Apply targeted post-processing based on analysis
            let refinedText = currentResult.text;

            // Pass 1: Focus on structural and lexical improvements
            if (passCount === 1) {
                console.log('Pass 1: Structural and lexical refinement');
                refinedText = applyStructuralRefinements(refinedText, aiAnalysis);
                refinedText = applyLexicalDiversityEnhancements(refinedText);
            }

            // Pass 2: Focus on syntactic variations and flow improvements
            else if (passCount === 2) {
                console.log('Pass 2: Syntactic and flow refinement');
                refinedText = applySyntacticVariations(refinedText, aiAnalysis);
                refinedText = improveTextualFlow(refinedText);
            }

            // Pass 3: Final polish with aggressive humanization
            else if (passCount === 3) {
                console.log('Pass 3: Final aggressive humanization');
                refinedText = applyFinalHumanizationPolish(refinedText, aiAnalysis);
            }

            // Update current result
            currentResult = {
                ...currentResult,
                text: refinedText,
                multiPass: true,
                passCount: passCount
            };

            // Check improvement
            const newAnalysis = analyzeRemainingAIPatterns(refinedText);
            const improvement = aiAnalysis.riskScore - newAnalysis.riskScore;
            console.log(`Pass ${passCount} improvement: ${improvement.toFixed(1)} points (${aiAnalysis.riskScore} → ${newAnalysis.riskScore})`);

            // If no significant improvement, stop
            if (improvement < 0.5 && passCount > 1) {
                console.log('Minimal improvement detected, stopping refinement');
                break;
            }
        }

        const refinementTime = Date.now() - startTime;
        console.log(`🎯 Multi-pass refinement completed in ${refinementTime}ms (${passCount} passes)`);

        return {
            ...currentResult,
            refinementTime: refinementTime,
            totalPasses: passCount,
            finalRiskScore: analyzeRemainingAIPatterns(currentResult.text).riskScore
        };

    } catch (error) {
        console.error('Multi-pass refinement error:', error.message);
        return initialResult; // Return original result if refinement fails
    }
}

/**
 * Apply structural refinements to eliminate formal patterns
 */
function applyStructuralRefinements(text, aiAnalysis) {
    let refined = text;

    // Target specific structural issues identified in analysis
    aiAnalysis.issues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                // Already handled in main transformation, but add more aggressive replacements
                refined = refined.replace(/\b(furthermore|moreover|additionally)\b/gi, () => {
                    const casual = ['and', 'plus', 'also', 'oh, and', 'by the way'];
                    return casual[Math.floor(Math.random() * casual.length)];
                });
                break;

            case 'mechanicalConclusions':
                refined = refined.replace(/\b(in conclusion|to summarize|in summary)\b/gi, () => {
                    const casual = ['so basically', 'bottom line', 'long story short', 'anyway'];
                    return casual[Math.floor(Math.random() * casual.length)];
                });
                break;

            case 'passiveOveruse':
                // Convert passive to active voice more aggressively
                refined = refined.replace(/\b(is|are|was|were)\s+(\w+ed)\b/gi, (match, _verb, pastParticiple) => {
                    if (Math.random() < 0.6) {
                        const activeAlternatives = [
                            `people ${pastParticiple.replace('ed', '')}`,
                            `we ${pastParticiple.replace('ed', '')}`,
                            `they ${pastParticiple.replace('ed', '')}`
                        ];
                        return activeAlternatives[Math.floor(Math.random() * activeAlternatives.length)];
                    }
                    return match;
                });
                break;
        }
    });

    return refined;
}

/**
 * Apply lexical diversity enhancements
 */
function applyLexicalDiversityEnhancements(text) {
    let enhanced = text;

    // Replace overused academic/formal words with varied alternatives
    const diversityReplacements = {
        'significant': ['big', 'major', 'important', 'key', 'huge', 'massive'],
        'substantial': ['big', 'large', 'major', 'considerable', 'hefty'],
        'comprehensive': ['complete', 'full', 'thorough', 'detailed', 'in-depth'],
        'extensive': ['wide', 'broad', 'large', 'huge', 'massive', 'wide-ranging'],
        'numerous': ['many', 'lots of', 'tons of', 'plenty of', 'loads of'],
        'various': ['different', 'many', 'several', 'multiple', 'all sorts of'],
        'utilize': ['use', 'employ', 'work with', 'make use of'],
        'implement': ['do', 'carry out', 'put in place', 'set up'],
        'facilitate': ['help', 'make easier', 'assist', 'enable']
    };

    Object.entries(diversityReplacements).forEach(([formal, alternatives]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        enhanced = enhanced.replace(regex, (match) => {
            if (Math.random() < 0.7) {
                const alt = alternatives[Math.floor(Math.random() * alternatives.length)];
                return match[0] === match[0].toUpperCase() ?
                       alt.charAt(0).toUpperCase() + alt.slice(1) : alt;
            }
            return match;
        });
    });

    return enhanced;
}

/**
 * Apply syntactic variations to break AI detection patterns
 */
function applySyntacticVariations(text, _aiAnalysis) {
    let varied = text;

    // Break up long sentences more aggressively
    varied = varied.replace(/([^.!?]{80,}),\s*([^.!?]{30,})/g, (_match, part1, part2) => {
        const breakPatterns = [
            `${part1}. ${part2.charAt(0).toUpperCase()}${part2.slice(1)}`,
            `${part1} - ${part2}`,
            `${part1}. And ${part2}`,
            `${part1}. Plus, ${part2}`
        ];
        return breakPatterns[Math.floor(Math.random() * breakPatterns.length)];
    });

    // Add more sentence variety
    varied = varied.replace(/^([A-Z][^.!?]*[.!?])/gm, (match) => {
        if (Math.random() < 0.3) {
            const starters = ['Look, ', 'Listen, ', 'So ', 'Well, ', 'Actually, ', 'Honestly, '];
            const starter = starters[Math.floor(Math.random() * starters.length)];
            return starter + match.charAt(0).toLowerCase() + match.slice(1);
        }
        return match;
    });

    return varied;
}

/**
 * Improve textual flow with natural transitions
 */
function improveTextualFlow(text) {
    let improved = text;

    // Add natural connectors between sentences
    improved = improved.replace(/\.\s+([A-Z])/g, (match, letter) => {
        if (Math.random() < 0.25) {
            const connectors = ['. And ', '. But ', '. So ', '. Plus, ', '. Also, ', '. Oh, and '];
            const connector = connectors[Math.floor(Math.random() * connectors.length)];
            return connector + letter.toLowerCase();
        }
        return match;
    });

    // Add casual interjections
    improved = improved.replace(/\b(important|crucial|key|essential)\b/gi, (match) => {
        if (Math.random() < 0.2) {
            const interjections = [
                `${match} (seriously)`,
                `${match} (trust me)`,
                `${match} (no joke)`,
                `really ${match.toLowerCase()}`
            ];
            return interjections[Math.floor(Math.random() * interjections.length)];
        }
        return match;
    });

    return improved;
}

/**
 * Apply final humanization polish
 */
function applyFinalHumanizationPolish(text, _aiAnalysis) {
    let polished = text;

    // Final aggressive transformation of any remaining formal patterns
    polished = applyUltraAggressiveTransformations(polished);

    // Add final human touches
    polished = polished.replace(/\b(really|very|quite)\s+(good|bad|important|useful)\b/gi, (match, _intensifier, adjective) => {
        const casualIntensifiers = [
            `super ${adjective}`,
            `crazy ${adjective}`,
            `insanely ${adjective}`,
            `ridiculously ${adjective}`,
            `${adjective} as hell`
        ];
        return Math.random() < 0.3 ?
               casualIntensifiers[Math.floor(Math.random() * casualIntensifiers.length)] :
               match;
    });

    // Final error injection for authenticity
    polished = injectHumanLikeErrors(polished, 0.06);

    return polished;
}

/**
 * Advanced AI pattern detection system
 * Comprehensive analysis of subtle AI writing signatures
 */
function analyzeRemainingAIPatterns(text) {
    const patterns = {
        // Level 1: Obvious AI patterns (high weight)
        formalTransitions: {
            pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly|specifically|essentially|ultimately)\b/gi,
            weight: 3,
            description: 'Formal transition words'
        },
        roboticPhrases: {
            pattern: /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized|it is essential to understand|it should be understood)\b/gi,
            weight: 4,
            description: 'Robotic qualifying phrases'
        },
        mechanicalConclusions: {
            pattern: /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly|overall|in essence)\b/gi,
            weight: 3,
            description: 'Mechanical conclusion phrases'
        },

        // Level 2: Subtle AI patterns (medium weight)
        overqualification: {
            pattern: /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various|multiple|several|diverse)\b/gi,
            weight: 2,
            description: 'Over-qualification words'
        },
        passiveOveruse: {
            pattern: /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
            weight: 2,
            description: 'Passive voice overuse'
        },
        technicalJargon: {
            pattern: /\b(utilize|implement|facilitate|optimize|demonstrate|establish|maintain|generate|analyze|evaluate)\b/gi,
            weight: 2,
            description: 'Technical jargon'
        },

        // Level 3: Advanced AI signatures (medium weight)
        perfectGrammar: {
            pattern: /^[A-Z][^.!?]*[.!?]$/gm,
            weight: 1,
            description: 'Perfect sentence structure',
            customAnalysis: (text) => {
                const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
                const perfectSentences = sentences.filter(s => /^[A-Z][^.!?]*$/.test(s.trim()));
                return perfectSentences.length > sentences.length * 0.8 ? perfectSentences.length : 0;
            }
        },
        consistentLength: {
            pattern: null,
            weight: 2,
            description: 'Consistent sentence lengths',
            customAnalysis: (text) => {
                const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
                if (sentences.length < 3) return 0;

                const lengths = sentences.map(s => s.length);
                const avgLength = lengths.reduce((sum, len) => sum + len, 0) / lengths.length;
                const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
                const stdDev = Math.sqrt(variance);

                // Low standard deviation indicates consistent lengths (AI-like)
                return stdDev < 15 ? Math.floor(sentences.length / 3) : 0;
            }
        },

        // Level 4: Lexical patterns (low weight but important)
        repetitiveStructure: {
            pattern: /^[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]/gm,
            weight: 2,
            description: 'Repetitive sentence structures'
        },
        lackOfContractions: {
            pattern: null,
            weight: 1,
            description: 'Lack of contractions',
            customAnalysis: (text) => {
                const contractableWords = text.match(/\b(do not|will not|cannot|should not|would not|could not|it is|that is|there is|we are|they are|you are)\b/gi) || [];
                const contractions = text.match(/\b(don't|won't|can't|shouldn't|wouldn't|couldn't|it's|that's|there's|we're|they're|you're)\b/gi) || [];

                const totalContractable = contractableWords.length + contractions.length;
                if (totalContractable === 0) return 0;

                const contractionRate = contractions.length / totalContractable;
                return contractionRate < 0.3 ? Math.floor(contractableWords.length / 2) : 0;
            }
        },

        // Level 5: Semantic patterns (advanced detection)
        semanticRepetition: {
            pattern: null,
            weight: 1,
            description: 'Semantic repetition patterns',
            customAnalysis: (text) => {
                const words = text.toLowerCase().match(/\b\w{4,}\b/g) || [];
                const wordFreq = {};
                words.forEach(word => {
                    wordFreq[word] = (wordFreq[word] || 0) + 1;
                });

                const overusedWords = Object.entries(wordFreq).filter(([word, freq]) =>
                    freq > 3 && !['that', 'this', 'with', 'from', 'they', 'have', 'been', 'were', 'will', 'would', 'could', 'should'].includes(word)
                );

                return overusedWords.length;
            }
        },

        // Level 6: Stylistic uniformity
        stylisticUniformity: {
            pattern: null,
            weight: 1,
            description: 'Stylistic uniformity',
            customAnalysis: (text) => {
                const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
                if (sentences.length < 4) return 0;

                // Check for uniform sentence beginnings
                const beginnings = sentences.map(s => s.trim().split(' ')[0].toLowerCase());
                const uniqueBeginnings = new Set(beginnings);
                const diversityRatio = uniqueBeginnings.size / beginnings.length;

                return diversityRatio < 0.6 ? Math.floor(sentences.length / 4) : 0;
            }
        }
    };

    let riskScore = 0;
    const issues = [];
    const detailedAnalysis = {};

    Object.entries(patterns).forEach(([category, config]) => {
        let matches = [];
        let count = 0;

        if (config.customAnalysis) {
            count = config.customAnalysis(text);
            if (count > 0) {
                matches = [`${count} instances detected`];
            }
        } else if (config.pattern) {
            matches = text.match(config.pattern) || [];
            count = matches.length;
        }

        if (count > 0) {
            const weightedScore = count * config.weight;
            riskScore += weightedScore;

            issues.push({
                category,
                count,
                weight: config.weight,
                weightedScore,
                description: config.description,
                examples: matches.slice(0, 3)
            });
        }

        detailedAnalysis[category] = {
            count,
            weight: config.weight,
            description: config.description
        };
    });

    // Additional sophisticated checks
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;

    // Check for overly long sentences (AI tendency)
    if (avgSentenceLength > 30) {
        riskScore += 3;
        issues.push({
            category: 'longSentences',
            count: 1,
            weight: 3,
            weightedScore: 3,
            description: 'Overly long sentences',
            examples: [`Average sentence length: ${avgSentenceLength.toFixed(1)} characters`]
        });
    }

    // Check for lack of casual language
    const casualMarkers = text.match(/\b(really|pretty|quite|sort of|kind of|basically|actually|honestly|seriously)\b/gi) || [];
    const casualDensity = casualMarkers.length / (text.length / 100); // per 100 characters

    if (casualDensity < 0.5) {
        riskScore += 2;
        issues.push({
            category: 'lackOfCasualLanguage',
            count: 1,
            weight: 2,
            weightedScore: 2,
            description: 'Insufficient casual language markers',
            examples: [`Casual density: ${casualDensity.toFixed(2)} per 100 chars`]
        });
    }

    return {
        riskScore: Math.min(riskScore, 20), // Increased cap for more nuanced scoring
        issues,
        detailedAnalysis,
        recommendations: generateAdvancedRefinementRecommendations(issues),
        textStats: {
            sentenceCount: sentences.length,
            avgSentenceLength: avgSentenceLength.toFixed(1),
            casualDensity: casualDensity.toFixed(2),
            wordCount: text.split(/\s+/).length
        }
    };
}

/**
 * Generate targeted refinement prompt based on AI pattern analysis
 */
function generateRefinementPrompt(text, aiAnalysis, options) {
    const { targetDetection, aggressiveness, originalText } = options;

    const issueDescriptions = aiAnalysis.issues.map(issue =>
        `- ${issue.category}: ${issue.count} instances (e.g., "${issue.examples[0]}")`
    ).join('\n');

    return `You are performing a CRITICAL REFINEMENT PASS to eliminate remaining AI detection patterns. The text below has already been humanized but still contains ${aiAnalysis.riskScore}/10 AI risk factors.

🎯 REFINEMENT MISSION: Achieve ≤${targetDetection}% AI detection by targeting these specific issues:

${issueDescriptions}

🔧 TARGETED REFINEMENT STRATEGIES:
1. ELIMINATE FORMAL TRANSITIONS: Replace with natural thought connectors
2. BREAK REPETITIVE PATTERNS: Vary sentence structures dramatically
3. INJECT HUMAN SPONTANEITY: Add natural digressions and personal touches
4. REDUCE PASSIVE VOICE: Convert to active, engaging language
5. ADD CONVERSATIONAL ELEMENTS: Include natural speech patterns
6. VARY SENTENCE RHYTHM: Mix short punchy sentences with flowing longer ones

🚫 CRITICAL ELIMINATION TARGETS:
- All instances of "furthermore", "moreover", "consequently"
- Robotic phrases like "it is important to note"
- Mechanical conclusions and summaries
- Overuse of qualifying adjectives
- Predictable sentence patterns

✅ PRESERVE COMPLETELY:
- Core message and factual content
- Technical accuracy and data
- Professional credibility
- Logical flow and structure

TEXT TO REFINE:
${text}

ULTRA-HUMANIZED REFINEMENT (≤${targetDetection}% AI detection):`;
}

/**
 * Generate advanced refinement recommendations based on detailed pattern analysis
 */
function generateAdvancedRefinementRecommendations(issues) {
    const recommendations = [];
    const prioritizedIssues = issues.sort((a, b) => b.weightedScore - a.weightedScore);

    prioritizedIssues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                recommendations.push({
                    priority: 'high',
                    action: 'Replace formal transitions with casual connectors',
                    examples: ['furthermore → and', 'consequently → so', 'moreover → plus'],
                    impact: 'Reduces formal tone significantly'
                });
                break;

            case 'roboticPhrases':
                recommendations.push({
                    priority: 'high',
                    action: 'Eliminate robotic qualifying phrases',
                    examples: ['it is important to note → here\'s the thing', 'it should be mentioned → by the way'],
                    impact: 'Removes AI-like hedging language'
                });
                break;

            case 'mechanicalConclusions':
                recommendations.push({
                    priority: 'high',
                    action: 'Use casual conclusion phrases',
                    examples: ['in conclusion → so basically', 'to summarize → bottom line'],
                    impact: 'Makes endings sound more natural'
                });
                break;

            case 'overqualification':
                recommendations.push({
                    priority: 'medium',
                    action: 'Replace over-qualification words with simpler alternatives',
                    examples: ['comprehensive → complete', 'substantial → big', 'numerous → lots of'],
                    impact: 'Reduces academic formality'
                });
                break;

            case 'passiveOveruse':
                recommendations.push({
                    priority: 'medium',
                    action: 'Convert passive voice to active voice',
                    examples: ['is implemented → we implement', 'was created → someone created'],
                    impact: 'Makes text more direct and engaging'
                });
                break;

            case 'technicalJargon':
                recommendations.push({
                    priority: 'medium',
                    action: 'Replace technical jargon with everyday language',
                    examples: ['utilize → use', 'facilitate → help', 'implement → do'],
                    impact: 'Makes text more accessible and human-like'
                });
                break;

            case 'perfectGrammar':
                recommendations.push({
                    priority: 'low',
                    action: 'Introduce minor grammatical variations',
                    examples: ['Add sentence fragments', 'Use casual punctuation'],
                    impact: 'Breaks perfect AI grammar patterns'
                });
                break;

            case 'consistentLength':
                recommendations.push({
                    priority: 'medium',
                    action: 'Vary sentence lengths dramatically',
                    examples: ['Mix short punchy sentences with longer explanations'],
                    impact: 'Creates more natural rhythm'
                });
                break;

            case 'lackOfContractions':
                recommendations.push({
                    priority: 'medium',
                    action: 'Add more contractions and casual language',
                    examples: ['do not → don\'t', 'it is → it\'s', 'we are → we\'re'],
                    impact: 'Increases conversational tone'
                });
                break;

            case 'semanticRepetition':
                recommendations.push({
                    priority: 'low',
                    action: 'Increase lexical diversity',
                    examples: ['Use synonyms for repeated words', 'Vary expressions'],
                    impact: 'Reduces robotic repetition patterns'
                });
                break;

            case 'stylisticUniformity':
                recommendations.push({
                    priority: 'low',
                    action: 'Diversify sentence beginnings and structures',
                    examples: ['Start with "Look,", "Listen,", "So"', 'Mix question and statement forms'],
                    impact: 'Creates more natural variation'
                });
                break;

            case 'longSentences':
                recommendations.push({
                    priority: 'high',
                    action: 'Break long sentences into shorter, punchier ones',
                    examples: ['Split at conjunctions', 'Use dashes for emphasis'],
                    impact: 'Improves readability and naturalness'
                });
                break;

            case 'lackOfCasualLanguage':
                recommendations.push({
                    priority: 'medium',
                    action: 'Inject more casual language markers',
                    examples: ['Add "really", "pretty", "quite"', 'Use "honestly", "actually"'],
                    impact: 'Increases conversational authenticity'
                });
                break;
        }
    });

    return recommendations;
}

/**
 * Generate specific recommendations for refinement (legacy function)
 */
function generateRefinementRecommendations(issues) {
    const recommendations = [];

    issues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                recommendations.push('Replace formal transitions with conversational connectors');
                break;
            case 'roboticPhrases':
                recommendations.push('Eliminate robotic qualifying phrases');
                break;
            case 'mechanicalConclusions':
                recommendations.push('Use natural ending patterns instead of formal conclusions');
                break;
            case 'overqualification':
                recommendations.push('Reduce excessive qualifying adjectives');
                break;
            case 'passiveOveruse':
                recommendations.push('Convert passive voice to active constructions');
                break;
            case 'repetitiveStructure':
                recommendations.push('Vary sentence structures and lengths');
                break;
            case 'longSentences':
                recommendations.push('Break up overly long sentences');
                break;
        }
    });

    return recommendations;
}

/**
 * Perplexity-based validation to ensure natural language flow
 * Lower perplexity = more predictable (AI-like), Higher perplexity = more natural (human-like)
 */
function calculateTextPerplexity(text) {
    // Simplified perplexity calculation based on linguistic patterns
    const words = text.toLowerCase().match(/\b\w+\b/g) || [];
    if (words.length < 10) return { score: 0, quality: 'insufficient_data' };

    // Calculate word frequency distribution
    const wordFreq = {};
    words.forEach(word => {
        wordFreq[word] = (wordFreq[word] || 0) + 1;
    });

    // Calculate entropy (measure of unpredictability)
    let entropy = 0;
    const totalWords = words.length;

    Object.values(wordFreq).forEach(freq => {
        const probability = freq / totalWords;
        entropy -= probability * Math.log2(probability);
    });

    // Calculate lexical diversity
    const uniqueWords = Object.keys(wordFreq).length;
    const lexicalDiversity = uniqueWords / totalWords;

    // Calculate sentence length variance (natural text has varied sentence lengths)
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 5);
    const sentenceLengths = sentences.map(s => s.length);
    const avgSentenceLength = sentenceLengths.reduce((sum, len) => sum + len, 0) / sentenceLengths.length;
    const sentenceVariance = sentenceLengths.reduce((sum, len) => sum + Math.pow(len - avgSentenceLength, 2), 0) / sentenceLengths.length;

    // Calculate bigram surprisal (how unexpected word pairs are)
    const bigrams = [];
    for (let i = 0; i < words.length - 1; i++) {
        bigrams.push(`${words[i]} ${words[i + 1]}`);
    }

    const bigramFreq = {};
    bigrams.forEach(bigram => {
        bigramFreq[bigram] = (bigramFreq[bigram] || 0) + 1;
    });

    const uniqueBigrams = Object.keys(bigramFreq).length;
    const bigramDiversity = uniqueBigrams / bigrams.length;

    // Composite perplexity score (0-100, higher = more natural)
    const entropyScore = Math.min(entropy / 4, 1) * 25; // Max 25 points
    const diversityScore = lexicalDiversity * 25; // Max 25 points
    const varianceScore = Math.min(Math.sqrt(sentenceVariance) / 20, 1) * 25; // Max 25 points
    const bigramScore = bigramDiversity * 25; // Max 25 points

    const perplexityScore = entropyScore + diversityScore + varianceScore + bigramScore;

    // Determine quality level
    let quality;
    if (perplexityScore >= 80) quality = 'excellent';
    else if (perplexityScore >= 65) quality = 'good';
    else if (perplexityScore >= 50) quality = 'acceptable';
    else if (perplexityScore >= 35) quality = 'poor';
    else quality = 'very_poor';

    return {
        score: Math.round(perplexityScore),
        quality,
        components: {
            entropy: Math.round(entropyScore),
            lexicalDiversity: Math.round(diversityScore),
            sentenceVariance: Math.round(varianceScore),
            bigramDiversity: Math.round(bigramScore)
        },
        stats: {
            totalWords: totalWords,
            uniqueWords: uniqueWords,
            avgSentenceLength: Math.round(avgSentenceLength),
            sentenceCount: sentences.length
        }
    };
}

/**
 * Validate text quality using perplexity and AI pattern analysis
 */
function validateTextQuality(text, targetDetection = 10) {
    const perplexity = calculateTextPerplexity(text);
    const aiAnalysis = analyzeRemainingAIPatterns(text);

    // Calculate composite quality score
    const perplexityWeight = 0.4;
    const aiPatternWeight = 0.6;

    // Invert AI risk score (lower risk = higher quality)
    const aiQualityScore = Math.max(0, 100 - (aiAnalysis.riskScore * 5));

    const compositeScore = (perplexity.score * perplexityWeight) + (aiQualityScore * aiPatternWeight);

    // Determine if text meets quality standards
    const meetsPerplexityStandard = perplexity.score >= 50;
    const meetsAIDetectionTarget = aiAnalysis.riskScore <= (targetDetection / 2); // More strict internal target
    const meetsCompositeStandard = compositeScore >= 60;

    const overallQuality = meetsPerplexityStandard && meetsAIDetectionTarget && meetsCompositeStandard;

    return {
        overallQuality,
        compositeScore: Math.round(compositeScore),
        perplexity,
        aiAnalysis,
        recommendations: overallQuality ? [] : generateQualityImprovementRecommendations(perplexity, aiAnalysis),
        meetsStandards: {
            perplexity: meetsPerplexityStandard,
            aiDetection: meetsAIDetectionTarget,
            composite: meetsCompositeStandard
        }
    };
}

/**
 * Generate recommendations for improving text quality
 */
function generateQualityImprovementRecommendations(perplexity, aiAnalysis) {
    const recommendations = [];

    if (perplexity.score < 50) {
        if (perplexity.components.entropy < 15) {
            recommendations.push('Increase vocabulary diversity - use more varied word choices');
        }
        if (perplexity.components.lexicalDiversity < 15) {
            recommendations.push('Reduce word repetition - find synonyms for frequently used terms');
        }
        if (perplexity.components.sentenceVariance < 15) {
            recommendations.push('Vary sentence lengths more dramatically - mix short and long sentences');
        }
        if (perplexity.components.bigramDiversity < 15) {
            recommendations.push('Use more varied word combinations and phrases');
        }
    }

    if (aiAnalysis.riskScore > 5) {
        recommendations.push('Apply more aggressive AI pattern elimination');
        recommendations.push('Increase casual language markers and contractions');
        recommendations.push('Break up formal sentence structures');
    }

    return recommendations;
}

/**
 * Enhanced validation with more sophisticated AI pattern detection
 */
async function validateDetectionTarget(text, targetDetection) {
    // Enhanced heuristic validation - in production, this would call an AI detection API

    const aiPatterns = [
        { pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence)\b/gi, weight: 3 },
        { pattern: /\b(it is important to note|it should be noted|it is worth mentioning)\b/gi, weight: 4 },
        { pattern: /\b(in conclusion|to summarize|in summary)\b/gi, weight: 3 },
        { pattern: /\b(comprehensive|extensive|significant|substantial|considerable)\b/gi, weight: 2 },
        { pattern: /\b(numerous|various|multiple|several)\s+\w+/gi, weight: 1 },
        { pattern: /\b(is|are|was|were)\s+\w+ed\b/gi, weight: 1 }
    ];

    let weightedScore = 0;
    let totalMatches = 0;

    aiPatterns.forEach(({ pattern, weight }) => {
        const matches = text.match(pattern) || [];
        weightedScore += matches.length * weight;
        totalMatches += matches.length;
    });

    // Calculate estimated detection percentage
    const textLength = text.length;
    const density = (weightedScore / textLength) * 1000; // Patterns per 1000 characters
    const estimatedDetection = Math.min(density * 8, 95); // Adjusted multiplier

    console.log(`Detection validation: ${totalMatches} patterns, density: ${density.toFixed(2)}, estimated: ${estimatedDetection.toFixed(1)}%`);

    return estimatedDetection <= targetDetection;
}

/**
 * Check if advanced LLM service is available
 */
export function isAdvancedLLMAvailable() {
    // Check if at least one API key is configured
    const availableKeys = [
        'FIREWORKS_API_KEY',
        'NOVITA_API_KEY', 
        'OPENROUTER_API_KEY',
        'GROQ_API_KEY'
    ].filter(key => process.env[key]);
    
    return availableKeys.length > 0;
}

/**
 * Get status of available providers
 */
export function getProviderStatus() {
    const status = {};
    
    Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
        status[modelName] = config.providers.map(provider => ({
            name: provider.name,
            available: !!process.env[provider.apiKeyEnv],
            model: provider.model
        }));
    });
    
    return status;
}
