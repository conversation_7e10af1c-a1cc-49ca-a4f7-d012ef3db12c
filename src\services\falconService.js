/**
 * Falcon/DeepSeek-R1 Humanization Service
 * High-performance text humanization using advanced language models
 * Replaces pattern-based approach with LLM-based humanization for ≤10% AI detection
 */

import axios from 'axios';
import { validateWithRealTimeDetection, isRealTimeDetectionAvailable } from './aiDetectionService.js';

// Model configurations for different providers - DeepSeek-R1 Primary with Falcon fallback
const MODEL_CONFIGS = {
    // Primary DeepSeek-R1 with DeepThink reasoning capabilities
    'deepseek-r1': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/deepseek-r1',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 1,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            },
            {
                name: 'novita',
                endpoint: 'https://api.novita.ai/v3/openai/chat/completions',
                model: 'deepseek-r1',
                apiKeyEnv: 'NOVITA_API_KEY',
                priority: 2,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            },
            {
                name: 'openrouter',
                endpoint: 'https://openrouter.ai/api/v1/chat/completions',
                model: 'deepseek/deepseek-r1',
                apiKeyEnv: 'OPENROUTER_API_KEY',
                priority: 3,
                supportsDeepThink: true,
                maxReasoningTokens: 1000
            }
        ]
    },
    // Secondary Falcon models for fallback
    'falcon-3-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 4
            },
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/falcon-3-7b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 5
            }
        ]
    },
    'falcon-h1-7b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon-H1-7B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon-H1-7B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-3-10b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/Falcon3-10B-Instruct/v1/chat/completions',
                model: 'tiiuae/Falcon3-10B-Instruct',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 1
            }
        ]
    },
    'falcon-180b': {
        providers: [
            {
                name: 'huggingface',
                endpoint: 'https://api-inference.huggingface.co/models/tiiuae/falcon-180B-chat/v1/chat/completions',
                model: 'tiiuae/falcon-180B-chat',
                apiKeyEnv: 'HUGGINGFACE_API_TOKEN',
                priority: 3 // Lower priority due to cost
            }
        ]
    },

    'llama-3.1-8b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/llama-v3p1-8b-instruct',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 6
            },
            {
                name: 'groq',
                endpoint: 'https://api.groq.com/openai/v1/chat/completions',
                model: 'llama-3.1-8b-instant',
                apiKeyEnv: 'GROQ_API_KEY',
                priority: 7
            }
        ]
    },
    'mistral-7b': {
        providers: [
            {
                name: 'fireworks',
                endpoint: 'https://api.fireworks.ai/inference/v1/chat/completions',
                model: 'accounts/fireworks/models/mistral-7b-instruct-4k',
                apiKeyEnv: 'FIREWORKS_API_KEY',
                priority: 8
            }
        ]
    }
};

/**
 * ZeroGPT-specific detection patterns that must be eliminated
 */
const ZEROGPT_DETECTION_PATTERNS = {
    formalTransitions: ['furthermore', 'moreover', 'additionally', 'consequently', 'therefore', 'thus', 'hence', 'subsequently', 'nevertheless'],
    roboticQualifiers: ['it is important to note', 'it should be mentioned', 'it is worth noting', 'it must be emphasized', 'it is essential to understand'],
    passiveConstructions: ['is implemented', 'are utilized', 'can be achieved', 'has been developed', 'will be conducted', 'are being processed'],
    corporateJargon: ['leverage', 'optimize', 'facilitate', 'comprehensive', 'strategic', 'innovative', 'synergistic', 'paradigm', 'methodology'],
    academicFormality: ['this study', 'the analysis', 'the findings', 'research indicates', 'data suggests', 'results demonstrate'],
    mechanicalStructures: ['firstly', 'secondly', 'thirdly', 'finally', 'in conclusion', 'to summarize', 'in summary'],
    perfectGrammar: /^[A-Z][^.!?]*[.!?]$/g, // Perfect sentence structure
    consistentLength: /^.{50,80}$/g // Consistent sentence lengths
};

/**
 * Generate ultra-aggressive humanization prompt optimized for DeepSeek-R1 with ZeroGPT targeting
 */
function generateHumanizationPrompt(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = false, // Changed default to false for more aggressive transformation
        targetDetection = 10,
        contentType = 'general',
        modelType = 'deepseek-r1',
        enableDeepThink = true
    } = options;

    const aggressivenessLevel = aggressiveness > 0.8 ? 'maximum' :
                               aggressiveness > 0.6 ? 'high' :
                               aggressiveness > 0.4 ? 'moderate' : 'conservative';

    // Ultra-aggressive DeepSeek-R1 prompt for maximum humanization transformation
    const deepThinkPrefix = enableDeepThink ? `<think>
I must COMPLETELY TRANSFORM this text to achieve ≤${targetDetection}% AI detection. This requires AGGRESSIVE changes, not subtle tweaks.

STEP 1: AGGRESSIVE AI PATTERN ELIMINATION
I need to DESTROY these AI patterns completely:
- ALL formal transitions (furthermore, moreover, additionally, consequently, therefore, thus, hence)
- ALL robotic qualifiers ("it is important to note", "it should be mentioned", "it is worth noting")
- ALL passive voice constructions ("is implemented", "are utilized", "can be achieved")
- ALL corporate jargon ("leverage", "optimize", "facilitate", "comprehensive", "strategic")
- ALL academic formality ("this study", "the analysis", "the findings suggest")
- ALL mechanical sentence structures and parallel lists

STEP 2: RADICAL HUMAN VOICE INJECTION
I will COMPLETELY REWRITE using natural human speech:
- Start sentences with "Look", "Listen", "Here's the thing", "You know what"
- Use tons of contractions: "don't", "won't", "can't", "it's", "that's", "we're"
- Add personal opinions: "I think", "honestly", "to be frank", "in my view"
- Include casual connectors: "and", "but", "so", "plus", "anyway", "basically"
- Use everyday vocabulary instead of formal terms
- Add conversational fillers: "well", "you see", "I mean", "actually"

STEP 3: EXTREME RESTRUCTURING STRATEGY
For EVERY sentence, I will:
1. COMPLETELY change the sentence structure (no similar patterns)
2. REPLACE every formal word with casual alternatives
3. BREAK long sentences into shorter, punchier ones
4. ADD human personality and opinion
5. INJECT natural speech patterns and rhythm
6. ENSURE it sounds like casual conversation, not formal writing

STEP 4: ZERO TOLERANCE VALIDATION
The result must be:
- COMPLETELY different sentence structures from original
- ZERO formal language or AI patterns remaining
- Sounds like a real person talking casually
- Maintains the core meaning but with totally different expression
- Would fool any AI detector including ZeroGPT

I will now AGGRESSIVELY transform this text with NO MERCY for formal language.
</think>

` : '';

    const prompt = `${deepThinkPrefix}You are an ULTRA-AGGRESSIVE text humanization specialist. Your job is to COMPLETELY DESTROY all AI patterns and RADICALLY TRANSFORM the text to achieve ≤${targetDetection}% AI detection on ZeroGPT and similar tools.

🚨 EXTREME TRANSFORMATION REQUIREMENTS:
- AI Detection Target: ≤${targetDetection}% (ZERO TOLERANCE for failure)
- Transformation Level: MAXIMUM AGGRESSIVE (${aggressivenessLevel} intensity)
- Content Type: ${contentType}
- Tone Strategy: CASUAL CONVERSATIONAL (forget formal tone - make it sound like a real person talking)
- Change Requirement: MINIMUM 70% of words must be different from original
- Structure: Break formal structures - use natural human speech patterns

⚡ ULTRA-AGGRESSIVE EXECUTION COMMANDS:
1. OBLITERATE every formal word and replace with casual alternatives
2. DESTROY all passive voice - make everything active and personal
3. INJECT massive amounts of contractions, casual language, and personal opinions
4. BREAK every long sentence into shorter, punchier human-style sentences
5. ADD conversational elements that no AI would naturally use
6. ELIMINATE all corporate/academic jargon completely

💥 EXTREME HUMANIZATION ATTACK PROTOCOLS:

1. SENTENCE DEMOLITION & RECONSTRUCTION:
   - SMASH every formal sentence structure completely
   - REBUILD using casual, conversational patterns
   - START sentences with: "Look", "Listen", "Here's the deal", "You know what"
   - END with natural conclusions: "...and that's it", "...pretty simple really"
   - MIX short punchy statements with longer rambling explanations

2. VOCABULARY ANNIHILATION:
   - DESTROY formal words: "utilize" → "use", "implement" → "do", "facilitate" → "help"
   - ELIMINATE corporate speak: "leverage" → "use", "optimize" → "make better"
   - REPLACE academic terms: "analysis" → "looking at", "methodology" → "way of doing things"
   - INJECT slang and casual terms: "stuff", "things", "pretty much", "basically"

3. MASSIVE HUMAN IMPERFECTION INJECTION (20-30% frequency):
   - FLOOD with hesitation: "I think", "maybe", "probably", "I guess", "sort of"
   - ADD redundancy: "really really", "pretty much exactly", "kind of sort of"
   - INSERT uncertainty: "I'm not 100% sure but", "could be wrong but", "seems like"
   - INCLUDE self-correction: "well, actually", "or rather", "I mean"

4. CONVERSATIONAL CHAOS INJECTION:
   - BREAK formal grammar rules intentionally
   - USE fragments: "Which is great." "Really important stuff." "Makes sense."
   - ADD run-on sentences with multiple "and"s and "but"s
   - INSERT casual interruptions: "anyway", "by the way", "oh and"

5. PERSONAL OPINION BOMBARDMENT:
   - INJECT personal views: "I think", "in my opinion", "honestly", "to be frank"
   - ADD emotional reactions: "which is awesome", "that's pretty cool", "kind of annoying"
   - INCLUDE experience references: "I've seen this before", "from what I know"
   - USE direct address: "you know", "you see", "you get what I mean"

6. ANTI-AI PATTERN WARFARE:
   - ELIMINATE all transition words completely
   - DESTROY parallel structures and lists
   - REMOVE all passive voice constructions
   - OBLITERATE formal conclusions and summaries

🎯 ZEROGPT DETECTION ELIMINATION TARGETS (ZERO TOLERANCE):
- FORMAL TRANSITIONS: ${ZEROGPT_DETECTION_PATTERNS.formalTransitions.join(', ')}
- ROBOTIC QUALIFIERS: ${ZEROGPT_DETECTION_PATTERNS.roboticQualifiers.join(', ')}
- PASSIVE CONSTRUCTIONS: ${ZEROGPT_DETECTION_PATTERNS.passiveConstructions.join(', ')}
- CORPORATE JARGON: ${ZEROGPT_DETECTION_PATTERNS.corporateJargon.join(', ')}
- ACADEMIC FORMALITY: ${ZEROGPT_DETECTION_PATTERNS.academicFormality.join(', ')}
- MECHANICAL STRUCTURES: ${ZEROGPT_DETECTION_PATTERNS.mechanicalStructures.join(', ')}
- PERFECT GRAMMAR: No perfectly structured sentences allowed
- CONSISTENT LENGTHS: Vary sentence lengths dramatically
- TECHNICAL PRECISION: Add human uncertainty and casual language

💪 TRANSFORMATION EXAMPLES:
BEFORE: "The implementation of this solution requires comprehensive analysis."
AFTER: "Look, if you want to actually do this thing, you've got to really dig into it and figure out what's going on."

BEFORE: "Furthermore, it is important to note that optimization is essential."
AFTER: "And here's the thing - you absolutely have to make this stuff work better. No question about it."

BEFORE: "The analysis reveals significant improvements in performance metrics."
AFTER: "So when we looked at this, turns out it actually makes things run way better. Pretty cool stuff."

🚨 MANDATORY REQUIREMENTS:
- MINIMUM 70% of words must be completely different
- ZERO formal language allowed
- MAXIMUM casual, conversational tone
- ADD personal opinions and uncertainty
- BREAK all formal sentence structures
- INJECT contractions and casual speech patterns

📝 CONTENT TO HUMANIZE:
${text}

🎭 ULTRA-HUMANIZED OUTPUT (Target: ≤${targetDetection}% ZeroGPT Detection):`;

    return prompt;
}

/**
 * Enhanced API call function with Falcon-specific optimizations
 */
async function callLLMAPI(provider, prompt, options = {}) {
    const {
        maxTokens = 4000,
        temperature = 0.7,
        topP = 0.9,
        timeout = 45000, // Increased timeout for reasoning models
        modelType = 'deepseek-r1',
        enableDeepThink = false,
        reasoningTokens = 1000
    } = options;

    const apiKey = process.env[provider.apiKeyEnv];
    if (!apiKey) {
        throw new Error(`API key not found for ${provider.name}: ${provider.apiKeyEnv}`);
    }

    // Optimize parameters for DeepSeek-R1 and other models with fine-tuned settings
    const optimizedParams = optimizeParametersForModel(provider.model, {
        temperature,
        topP,
        maxTokens,
        modelType,
        targetDetection: options.targetDetection || 10,
        enableDeepThink,
        reasoningTokens
    });

    const requestData = {
        model: provider.model,
        messages: [
            {
                role: 'user',
                content: prompt
            }
        ],
        max_tokens: optimizedParams.maxTokens,
        temperature: optimizedParams.temperature,
        top_p: optimizedParams.topP,
        stream: false
    };

    // Add fine-tuned model-specific parameters for ≤10% AI detection
    if (modelType === 'deepseek-r1') {
        // DeepSeek-R1 specific parameters for DeepThink reasoning
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.05;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.45;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.55;

        // DeepThink reasoning parameters
        if (optimizedParams.enableDeepThink) {
            requestData.reasoning_effort = 'high';
            requestData.max_reasoning_tokens = optimizedParams.reasoningTokens || 1000;
            console.log(`🧠 DeepThink enabled: ${optimizedParams.reasoningTokens} reasoning tokens`);
        }

        // Enhanced sampling for reasoning
        requestData.do_sample = true;
        requestData.top_k = 50;

    } else if (modelType === 'falcon') {
        requestData.repetition_penalty = optimizedParams.repetitionPenalty || 1.15;
        requestData.presence_penalty = optimizedParams.presencePenalty || 0.3;
        requestData.frequency_penalty = optimizedParams.frequencyPenalty || 0.4;
        requestData.do_sample = true;
        requestData.pad_token_id = 50256; // Ensure proper tokenization
    } else {
        // Apply optimized parameters for other models
        if (optimizedParams.repetitionPenalty) {
            requestData.repetition_penalty = optimizedParams.repetitionPenalty;
        }
        if (optimizedParams.presencePenalty) {
            requestData.presence_penalty = optimizedParams.presencePenalty;
        }
        if (optimizedParams.frequencyPenalty) {
            requestData.frequency_penalty = optimizedParams.frequencyPenalty;
        }
    }

    const headers = {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json'
    };

    // Add provider-specific headers and configurations
    if (provider.name === 'openrouter') {
        headers['HTTP-Referer'] = process.env.NEXT_PUBLIC_APP_URL || 'https://ghostlayer.netlify.app';
        headers['X-Title'] = 'GhostLayer';
    } else if (provider.name === 'huggingface') {
        headers['X-Use-Cache'] = 'false'; // Ensure fresh responses
        headers['X-Wait-For-Model'] = 'true'; // Wait for model to load
    }

    try {
        console.log(`Calling ${provider.name} API with Falcon model ${provider.model}...`);

        const startTime = Date.now();
        const response = await axios.post(provider.endpoint, requestData, {
            headers,
            timeout
        });
        const responseTime = Date.now() - startTime;

        if (response.data && response.data.choices && response.data.choices[0]) {
            const fullResponse = response.data.choices[0].message.content.trim();

            // Extract reasoning chain and final output for DeepSeek-R1
            const reasoningValidation = validateReasoningChain(fullResponse, modelType, optimizedParams);
            let humanizedText = reasoningValidation.finalOutput;

            // Apply ULTRA-AGGRESSIVE post-processing transformations for maximum humanization
            if (modelType === 'deepseek-r1' && optimizedParams.targetDetection <= 10) {
                console.log('🔥 Applying ULTRA-AGGRESSIVE post-processing transformations...');
                const originalLength = humanizedText.length;
                humanizedText = applyUltraAggressiveTransformations(humanizedText);
                const transformationRate = ((originalLength - humanizedText.length) / originalLength * 100);
                console.log(`   Transformation applied: ${transformationRate.toFixed(1)}% text change`);
            }

            // Validate response quality
            if (!validateFalconResponse(humanizedText, prompt)) {
                throw new Error(`${modelType} model response failed quality validation`);
            }

            return {
                success: true,
                text: humanizedText,
                provider: provider.name,
                model: provider.model,
                usage: response.data.usage || {},
                processingTime: responseTime,
                modelType: modelType,
                optimizedParams: optimizedParams,
                reasoningValidation: reasoningValidation
            };
        } else {
            throw new Error('Invalid response format from API');
        }

    } catch (error) {
        console.error(`${provider.name} API error:`, error.message);

        if (error.response) {
            const status = error.response.status;
            const message = error.response.data?.error?.message || error.response.data?.message || 'Unknown API error';

            // Handle Falcon-specific errors
            if (status === 503 && provider.name === 'huggingface') {
                throw new Error(`${provider.name} model loading (${status}): ${message}. Please retry in a few moments.`);
            }

            throw new Error(`${provider.name} API error (${status}): ${message}`);
        } else if (error.code === 'ECONNABORTED') {
            throw new Error(`${provider.name} API timeout - Falcon models may need more time`);
        } else {
            throw new Error(`${provider.name} API error: ${error.message}`);
        }
    }
}

/**
 * Fine-tuned parameters for DeepSeek-R1 and Falcon models optimized for ≤10% AI detection
 */
function optimizeParametersForModel(modelName, params) {
    const { temperature, topP, maxTokens, targetDetection = 10, enableDeepThink = false, reasoningTokens = 1000 } = params;

    // DeepSeek-R1: Primary model with DeepThink reasoning capabilities
    if (modelName.includes('deepseek-r1') || modelName.includes('DeepSeek-R1')) {
        // ULTRA-AGGRESSIVE parameter optimization for maximum transformation
        const baseTemp = targetDetection <= 5 ? 1.0 :     // Maximum creativity for ultra-strict
                        targetDetection <= 10 ? 0.98 :    // Very high for ≤10% target
                        0.95;                              // High for relaxed target

        const reasoningAllocation = enableDeepThink ? Math.min(reasoningTokens, maxTokens * 0.3) : 0;
        const outputTokens = maxTokens - reasoningAllocation;

        console.log(`💥 ULTRA-AGGRESSIVE DeepSeek-R1 Parameters:`);
        console.log(`   Target Detection: ≤${targetDetection}%`);
        console.log(`   DeepThink Enabled: ${enableDeepThink}`);
        console.log(`   Reasoning Tokens: ${reasoningAllocation}`);
        console.log(`   Output Tokens: ${outputTokens}`);
        console.log(`   Temperature: ${baseTemp} (MAXIMUM CREATIVITY)`);

        return {
            temperature: baseTemp,
            topP: 0.98, // Maximum creativity for radical transformation
            maxTokens: Math.min(outputTokens, 5000), // Increased for longer transformations
            repetitionPenalty: 1.0, // No penalty - let creativity flow
            presencePenalty: 0.7, // Maximum encouragement for topic diversity
            frequencyPenalty: 0.8, // Maximum word variation for human-like output
            reasoningTokens: reasoningAllocation,
            enableDeepThink: enableDeepThink,
            // Ultra-aggressive sampling for maximum transformation
            minP: 0.02, // Lower threshold for more creative sampling
            typicalP: 0.98, // Higher typical sampling for maximum randomness
            topK: 0 // Disable top-k completely
        };
    }

    // Falcon 3-7B: Balanced performance with optimized creativity
    if (modelName.includes('Falcon3-7B') || modelName.includes('falcon-3-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.85 : Math.min(temperature * 0.9, 0.8),
            topP: 0.88, // Slightly higher for more diverse vocabulary
            maxTokens: Math.min(maxTokens, 3800),
            repetitionPenalty: 1.15, // Increased to reduce AI-like repetition
            presencePenalty: 0.3, // Encourage topic diversity
            frequencyPenalty: 0.4 // Reduce word repetition
        };
    }

    // Falcon-H1-7B: Hybrid architecture optimized for efficiency and naturalness
    if (modelName.includes('Falcon-H1-7B') || modelName.includes('falcon-h1-7b')) {
        return {
            temperature: targetDetection <= 10 ? 0.82 : Math.min(temperature * 0.85, 0.75),
            topP: 0.86, // Focused but creative sampling
            maxTokens: Math.min(maxTokens, 3600),
            repetitionPenalty: 1.18, // Higher penalty for hybrid model
            presencePenalty: 0.25,
            frequencyPenalty: 0.35
        };
    }

    // Falcon 3-10B: High-performance model for demanding tasks
    if (modelName.includes('Falcon3-10B') || modelName.includes('falcon-3-10b')) {
        return {
            temperature: targetDetection <= 10 ? 0.88 : Math.min(temperature * 0.95, 0.85),
            topP: 0.90, // Higher creativity for larger model
            maxTokens: Math.min(maxTokens, 4000),
            repetitionPenalty: 1.12, // Lower penalty as larger model handles diversity better
            presencePenalty: 0.35,
            frequencyPenalty: 0.45
        };
    }

    // Falcon 180B: Premium model with maximum capabilities
    if (modelName.includes('falcon-180B') || modelName.includes('falcon-180b')) {
        return {
            temperature: targetDetection <= 10 ? 0.90 : Math.min(temperature * 1.0, 0.9),
            topP: 0.92, // Maximum creativity for largest model
            maxTokens: Math.min(maxTokens, 4500),
            repetitionPenalty: 1.08, // Minimal penalty for most capable model
            presencePenalty: 0.4,
            frequencyPenalty: 0.5
        };
    }



    // Llama optimizations
    if (modelName.includes('llama') || modelName.includes('Llama')) {
        return {
            temperature: targetDetection <= 10 ? temperature * 1.1 : temperature,
            topP: Math.min(topP, 0.9),
            maxTokens: maxTokens,
            repetitionPenalty: 1.05,
            presencePenalty: 0.15,
            frequencyPenalty: 0.25
        };
    }

    // Default parameters for unknown models
    return {
        temperature,
        topP,
        maxTokens,
        repetitionPenalty: 1.1,
        presencePenalty: 0.2,
        frequencyPenalty: 0.3
    };
}

/**
 * Apply ultra-aggressive post-processing transformations to ensure maximum humanization
 */
function applyUltraAggressiveTransformations(text) {
    let transformed = text;

    // 1. DESTROY formal transitions completely
    const formalTransitions = {
        'furthermore': ['and', 'plus', 'also', 'on top of that'],
        'moreover': ['and', 'plus', 'also', 'what\'s more'],
        'additionally': ['and', 'plus', 'also', 'on top of that'],
        'consequently': ['so', 'which means', 'that\'s why'],
        'therefore': ['so', 'which means', 'that\'s why'],
        'thus': ['so', 'which means', 'that way'],
        'hence': ['so', 'which is why', 'that\'s how'],
        'subsequently': ['then', 'after that', 'next'],
        'nevertheless': ['but', 'still', 'even so']
    };

    Object.entries(formalTransitions).forEach(([formal, casual]) => {
        const regex = new RegExp(`\\b${formal}\\b`, 'gi');
        transformed = transformed.replace(regex, () => {
            return casual[Math.floor(Math.random() * casual.length)];
        });
    });

    // 2. ELIMINATE robotic qualifiers
    const roboticQualifiers = {
        'it is important to note that': ['here\'s the thing -', 'look,', 'listen,'],
        'it should be mentioned that': ['oh, and', 'by the way,', 'also,'],
        'it is worth noting that': ['interesting thing is', 'what\'s cool is', 'here\'s what I noticed -'],
        'it must be emphasized that': ['seriously,', 'this is key -', 'here\'s what matters -']
    };

    Object.entries(roboticQualifiers).forEach(([robotic, human]) => {
        const regex = new RegExp(robotic.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
        transformed = transformed.replace(regex, () => {
            return human[Math.floor(Math.random() * human.length)];
        });
    });

    // 3. INJECT massive contractions
    const contractions = {
        'do not': 'don\'t',
        'will not': 'won\'t',
        'cannot': 'can\'t',
        'should not': 'shouldn\'t',
        'would not': 'wouldn\'t',
        'could not': 'couldn\'t',
        'it is': 'it\'s',
        'that is': 'that\'s',
        'there is': 'there\'s',
        'we are': 'we\'re',
        'they are': 'they\'re',
        'you are': 'you\'re'
    };

    Object.entries(contractions).forEach(([full, contracted]) => {
        const regex = new RegExp(`\\b${full}\\b`, 'gi');
        transformed = transformed.replace(regex, contracted);
    });

    // 4. ADD human imperfections and casual language
    transformed = transformed.replace(/\. ([A-Z])/g, (match, letter) => {
        const connectors = ['. And ', '. But ', '. So ', '. Plus ', '. Also '];
        if (Math.random() < 0.3) {
            return connectors[Math.floor(Math.random() * connectors.length)] + letter.toLowerCase();
        }
        return match;
    });

    // 5. INJECT casual qualifiers
    const casualQualifiers = ['pretty much', 'basically', 'sort of', 'kind of', 'more or less', 'I think', 'probably'];
    transformed = transformed.replace(/\b(is|are|will|can|should)\b/g, (match) => {
        if (Math.random() < 0.2) {
            const qualifier = casualQualifiers[Math.floor(Math.random() * casualQualifiers.length)];
            return `${qualifier} ${match}`;
        }
        return match;
    });

    return transformed;
}

/**
 * Validate reasoning chain for DeepSeek-R1 responses
 */
function validateReasoningChain(fullResponse, modelType, optimizedParams) {
    const validation = {
        hasReasoning: false,
        reasoningQuality: 0,
        finalOutput: fullResponse,
        reasoningLength: 0,
        reasoningSteps: 0
    };

    if (modelType === 'deepseek-r1' && optimizedParams.enableDeepThink) {
        // Check for reasoning tags
        const thinkMatch = fullResponse.match(/<think>([\s\S]*?)<\/think>/);

        if (thinkMatch) {
            validation.hasReasoning = true;
            const reasoningContent = thinkMatch[1].trim();
            validation.reasoningLength = reasoningContent.length;

            // Count reasoning steps
            validation.reasoningSteps = (reasoningContent.match(/STEP \d+/g) || []).length;

            // Extract final output (everything after </think>)
            validation.finalOutput = fullResponse.replace(/<think>[\s\S]*?<\/think>\s*/, '').trim();

            // Assess reasoning quality based on new aggressive approach
            const qualityIndicators = [
                reasoningContent.includes('AGGRESSIVE AI PATTERN ELIMINATION'),
                reasoningContent.includes('RADICAL HUMAN VOICE INJECTION'),
                reasoningContent.includes('EXTREME RESTRUCTURING STRATEGY'),
                reasoningContent.includes('ZERO TOLERANCE VALIDATION'),
                reasoningContent.includes('COMPLETELY TRANSFORM'),
                reasoningContent.includes('AGGRESSIVELY transform'),
                validation.reasoningSteps >= 4,
                validation.reasoningLength > 300
            ];

            validation.reasoningQuality = qualityIndicators.filter(Boolean).length / qualityIndicators.length;

            console.log(`🧠 DeepThink Reasoning Validation:`);
            console.log(`   Reasoning Found: ${validation.hasReasoning}`);
            console.log(`   Reasoning Length: ${validation.reasoningLength} chars`);
            console.log(`   Reasoning Steps: ${validation.reasoningSteps}`);
            console.log(`   Quality Score: ${(validation.reasoningQuality * 100).toFixed(1)}%`);

        } else {
            console.log(`⚠️  DeepThink enabled but no reasoning chain found in response`);
        }
    }

    return validation;
}

/**
 * Validate model response quality
 */
function validateFalconResponse(response, originalPrompt) {
    if (!response || response.length < 10) {
        return false;
    }

    // Check for common Falcon model issues
    if (response.includes('I cannot') || response.includes('I apologize')) {
        return false;
    }

    // Check for repetitive patterns (common in Falcon models)
    const sentences = response.split(/[.!?]+/);
    if (sentences.length > 3) {
        const uniqueSentences = new Set(sentences.map(s => s.trim().toLowerCase()));
        if (uniqueSentences.size / sentences.length < 0.7) {
            return false; // Too repetitive
        }
    }

    return true;
}

/**
 * Main humanization function using advanced LLMs
 */
export async function humanizeWithAdvancedLLM(text, options = {}) {
    const {
        aggressiveness = 0.7,
        maintainTone = true,
        targetDetection = 10,
        preferredModel = 'deepseek-r1',
        maxRetries = 2
    } = options;

    if (!text || typeof text !== 'string' || !text.trim()) {
        return {
            success: false,
            error: 'Input text is required and must be a non-empty string'
        };
    }

    const startTime = Date.now();
    
    // Analyze content type for better prompting
    const contentType = analyzeContentType(text);
    
    // Generate optimized prompt
    const prompt = generateHumanizationPrompt(text, {
        aggressiveness,
        maintainTone,
        targetDetection,
        contentType
    });

    // Enhanced intelligent model selection with content analysis
    const modelOrder = getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text);

    console.log(`🧠 DEEPSEEK-R1 MODEL SELECTION DEBUG:`);
    console.log(`   Preferred Model: ${preferredModel}`);
    console.log(`   Target Detection: ≤${targetDetection}%`);
    console.log(`   Model Order: ${modelOrder.join(' → ')}`);
    console.log(`   DeepSeek-R1 Priority: ${modelOrder.indexOf('deepseek-r1') + 1}/${modelOrder.length}`);

    for (const modelName of modelOrder) {
        const modelConfig = MODEL_CONFIGS[modelName];
        if (!modelConfig) {
            console.log(`❌ Model config not found: ${modelName}`);
            continue;
        }

        console.log(`\n🔄 Trying model: ${modelName}`);

        // Sort providers by priority
        const sortedProviders = modelConfig.providers.sort((a, b) => (a.priority || 999) - (b.priority || 999));
        console.log(`   Available providers: ${sortedProviders.map(p => p.name).join(', ')}`);

        // Try each provider for this model
        for (const provider of sortedProviders) {
            // Check if API key is available
            if (!process.env[provider.apiKeyEnv]) {
                console.log(`   ⚠️  Skipping ${provider.name}: API key not configured (${provider.apiKeyEnv})`);
                continue;
            }

            console.log(`   ✅ Using ${provider.name} for ${modelName}`);

            let retries = 0;
            while (retries <= maxRetries) {
                try {
                    // Determine model type and DeepThink activation
                    const isDeepSeekR1 = modelName.includes('deepseek-r1') || modelName.includes('deepseek');
                    const modelType = isDeepSeekR1 ? 'deepseek-r1' :
                                     modelName.includes('falcon') ? 'falcon' : 'other';

                    // Enable DeepThink for DeepSeek-R1
                    const enableDeepThink = isDeepSeekR1 && targetDetection <= 10;

                    if (enableDeepThink) {
                        console.log(`   🧠 DeepThink ACTIVATED for ${modelName} (≤${targetDetection}% target)`);
                    }

                    const result = await callLLMAPI(provider, prompt, {
                        maxTokens: Math.min(4500, text.length * 3), // Increased for DeepSeek-R1 reasoning
                        temperature: calculateOptimalTemperature(aggressiveness, modelType),
                        topP: isDeepSeekR1 ? 0.95 : (modelType === 'falcon' ? 0.85 : 0.9),
                        modelType: modelType,
                        targetDetection: targetDetection,
                        enableDeepThink: enableDeepThink,
                        reasoningTokens: enableDeepThink ? 1000 : 0
                    });

                    const totalTime = Date.now() - startTime;

                    console.log(`Successfully humanized with ${provider.name}/${modelName} in ${totalTime}ms`);

                    // Multi-pass processing for ≤10% AI detection targets
                    let finalResult = result;
                    if (targetDetection <= 10 && result.success) {
                        console.log(`Applying multi-pass refinement for ≤${targetDetection}% detection target...`);
                        finalResult = await applyMultiPassRefinement(result, {
                            originalText: text,
                            targetDetection,
                            aggressiveness,
                            modelName,
                            provider
                        });
                    }

                    // Real-time AI detection validation for ≤10% targets
                    let detectionValidation = null;
                    if (targetDetection <= 10 && isRealTimeDetectionAvailable()) {
                        console.log('Performing real-time AI detection validation...');
                        detectionValidation = await validateWithRealTimeDetection(finalResult.text, {
                            targetDetection,
                            preferredAPI: 'gptzero',
                            fallbackAPIs: ['originality', 'sapling']
                        });

                        if (detectionValidation.success && !detectionValidation.meetsTarget) {
                            console.log(`Real-time detection: ${detectionValidation.score.toFixed(1)}% > ${targetDetection}%`);

                            // Auto-retry with higher aggressiveness if detection score is too high
                            if (detectionValidation.recommendation.shouldRetry && retries === 0) {
                                console.log('Auto-retrying with increased aggressiveness...');
                                const newAggressiveness = Math.min(aggressiveness + detectionValidation.recommendation.suggestedAggressiveness, 1.0);

                                // Recursive retry with higher aggressiveness
                                return await humanizeWithAdvancedLLM(text, {
                                    aggressiveness: newAggressiveness,
                                    maintainTone,
                                    targetDetection,
                                    preferredModel: modelName,
                                    maxRetries: 0 // Prevent infinite recursion
                                });
                            }
                        }
                    }

                    // Fallback to heuristic validation if real-time detection unavailable
                    const validationResult = detectionValidation || await validateDetectionTarget(finalResult.text, targetDetection);
                    const meetsTarget = detectionValidation ? detectionValidation.meetsTarget : validationResult;

                    if (meetsTarget) {
                        return {
                            ...finalResult,
                            originalText: text,
                            processingTime: totalTime + (finalResult.refinementTime || 0),
                            method: finalResult.multiPass ? 'llm-advanced-falcon-multipass' : 'llm-advanced-falcon',
                            modelName,
                            detectionTarget: targetDetection,
                            detectionValidation: detectionValidation,
                            options: { aggressiveness, maintainTone, targetDetection }
                        };
                    } else {
                        const score = detectionValidation ? detectionValidation.score : 'unknown';
                        console.warn(`${modelName} result detection score ${score}% exceeds ≤${targetDetection}% target, trying next model`);
                        break; // Try next model instead of retrying same model
                    }

                } catch (error) {
                    retries++;
                    console.warn(`${provider.name} attempt ${retries}/${maxRetries + 1} failed:`, error.message);

                    if (retries <= maxRetries) {
                        // Progressive backoff with longer delays for Falcon models
                        const delay = modelType === 'falcon' ? 2000 * retries : 1000 * retries;
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
        }
    }

    // If all LLM attempts failed
    const totalTime = Date.now() - startTime;
    return {
        success: false,
        error: 'All advanced LLM providers failed',
        originalText: text,
        processingTime: totalTime,
        method: 'failed',
        fallbackRecommended: true
    };
}

/**
 * Enhanced content type analysis for better Falcon model prompting
 */
function analyzeContentType(text) {
    const formalIndicators = /\b(therefore|furthermore|consequently|moreover|nevertheless|thus|hence)\b/gi;
    const technicalIndicators = /\b(API|algorithm|implementation|configuration|optimization|framework|architecture|deployment)\b/gi;
    const academicIndicators = /\b(research|study|analysis|methodology|findings|hypothesis|conclusion|evidence)\b/gi;
    const businessIndicators = /\b(strategy|revenue|market|customer|business|sales|profit|ROI)\b/gi;
    const creativeIndicators = /\b(story|narrative|creative|artistic|design|aesthetic|inspiration)\b/gi;

    if (academicIndicators.test(text)) return 'academic';
    if (technicalIndicators.test(text)) return 'technical';
    if (businessIndicators.test(text)) return 'business';
    if (creativeIndicators.test(text)) return 'creative';
    if (formalIndicators.test(text)) return 'formal';
    return 'general';
}

/**
 * Enhanced intelligent model selection based on content analysis and target detection
 */
function getOptimalModelOrder(preferredModel, targetDetection, aggressiveness, text = '') {
    // Analyze content for AI patterns to determine optimal model selection
    const contentAnalysis = analyzeContentComplexity(text);

    // For ≤10% AI detection target, prioritize DeepSeek-R1 with DeepThink reasoning
    if (targetDetection <= 10) {
        console.log(`Content analysis: AI risk=${contentAnalysis.aiRisk}, complexity=${contentAnalysis.complexity}, length=${contentAnalysis.length}`);

        // High AI risk content - DeepSeek-R1 first with Falcon fallbacks
        if (contentAnalysis.aiRisk >= 7 || aggressiveness >= 0.8) {
            console.log('High AI risk detected, using DeepSeek-R1 with maximum power fallbacks');
            return ['deepseek-r1', 'falcon-180b', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', preferredModel];
        }

        // Medium AI risk with complex content - DeepSeek-R1 primary
        if (contentAnalysis.aiRisk >= 4 || contentAnalysis.complexity >= 6 || aggressiveness >= 0.6) {
            console.log('Medium-high AI risk, using DeepSeek-R1 with powerful fallbacks');
            return ['deepseek-r1', 'falcon-3-10b', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-180b', preferredModel];
        }

        // Standard ≤10% detection with moderate content - DeepSeek-R1 first
        if (aggressiveness >= 0.4) {
            console.log('Standard ≤10% detection, using DeepSeek-R1 with balanced fallbacks');
            return ['deepseek-r1', 'falcon-3-7b', 'falcon-h1-7b', 'falcon-3-10b', preferredModel, 'llama-3.1-8b'];
        }

        // Low aggressiveness but still ≤10% target - DeepSeek-R1 primary
        console.log('Low aggressiveness ≤10% target, using DeepSeek-R1 with efficient fallbacks');
        return ['deepseek-r1', 'falcon-h1-7b', 'falcon-3-7b', 'falcon-3-10b', preferredModel];
    }

    // For higher detection targets (>10%), still prioritize DeepSeek-R1 but with faster fallbacks
    if (targetDetection <= 20) {
        return ['deepseek-r1', 'falcon-3-7b', preferredModel, 'falcon-h1-7b', 'llama-3.1-8b', 'mistral-7b'];
    }

    // For relaxed detection targets (>20%), DeepSeek-R1 first for consistency
    return ['deepseek-r1', preferredModel, 'falcon-3-7b', 'llama-3.1-8b', 'mistral-7b'];
}

/**
 * Analyze content complexity and AI risk patterns
 */
function analyzeContentComplexity(text) {
    if (!text || typeof text !== 'string') {
        return { aiRisk: 0, complexity: 0, length: 0 };
    }

    let aiRisk = 0;
    let complexity = 0;

    // AI risk pattern analysis
    const highRiskPatterns = [
        /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly)\b/gi,
        /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi,
        /\b(in conclusion|to summarize|in summary|to conclude|finally|lastly)\b/gi,
        /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi
    ];

    const mediumRiskPatterns = [
        /\b(clearly|obviously|certainly|definitely|undoubtedly|unquestionably)\b/gi,
        /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
        /\b(implementation|optimization|utilization|maximization|minimization)\b/gi
    ];

    // Count high-risk patterns (weight: 2)
    highRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length * 2;
    });

    // Count medium-risk patterns (weight: 1)
    mediumRiskPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        aiRisk += matches.length;
    });

    // Complexity analysis
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
    const words = text.split(/\s+/).length;
    const uniqueWords = new Set(text.toLowerCase().split(/\s+/)).size;
    const lexicalDiversity = uniqueWords / words;

    // Calculate complexity score (0-10)
    if (avgSentenceLength > 35) complexity += 2;
    if (avgSentenceLength > 50) complexity += 2;
    if (words > 500) complexity += 1;
    if (words > 1000) complexity += 2;
    if (lexicalDiversity < 0.4) complexity += 2; // Low diversity = more complex to humanize
    if (lexicalDiversity < 0.3) complexity += 1;

    // Technical content indicators
    const technicalPatterns = [
        /\b(API|algorithm|implementation|configuration|optimization|framework|architecture)\b/gi,
        /\b(methodology|analysis|evaluation|assessment|validation|verification)\b/gi
    ];

    technicalPatterns.forEach(pattern => {
        const matches = text.match(pattern) || [];
        complexity += Math.min(matches.length, 3); // Cap technical complexity contribution
    });

    return {
        aiRisk: Math.min(aiRisk, 10), // Cap at 10
        complexity: Math.min(complexity, 10), // Cap at 10
        length: text.length,
        avgSentenceLength: avgSentenceLength.toFixed(1),
        lexicalDiversity: lexicalDiversity.toFixed(3),
        wordCount: words
    };
}

/**
 * Calculate optimal temperature based on aggressiveness and model type
 */
function calculateOptimalTemperature(aggressiveness, modelType) {
    const baseTemp = aggressiveness * 0.8 + 0.2;

    if (modelType === 'falcon') {
        // Falcon models work better with slightly lower temperature
        return Math.min(baseTemp * 0.9, 0.8);
    }

    return baseTemp;
}

/**
 * Multi-pass refinement for enhanced humanization targeting ≤10% AI detection
 */
async function applyMultiPassRefinement(initialResult, options) {
    const { originalText, targetDetection, aggressiveness, modelName, provider } = options;
    const startTime = Date.now();

    try {
        // Analyze the initial result for remaining AI patterns
        const aiAnalysis = analyzeRemainingAIPatterns(initialResult.text);

        if (aiAnalysis.riskScore <= 2) {
            console.log('Initial result already meets high quality standards, skipping refinement');
            return initialResult;
        }

        console.log(`AI risk score: ${aiAnalysis.riskScore}/10, applying targeted refinement...`);

        // Generate refinement prompt targeting specific issues
        const refinementPrompt = generateRefinementPrompt(initialResult.text, aiAnalysis, {
            targetDetection,
            aggressiveness: Math.min(aggressiveness + 0.2, 1.0), // Increase aggressiveness for refinement
            originalText
        });

        // Apply secondary refinement pass with DeepSeek-R1 optimized parameters
        const modelType = provider.model.includes('deepseek') ? 'deepseek-r1' : 'falcon';
        const refinementResult = await callLLMAPI(provider, refinementPrompt, {
            maxTokens: Math.min(4000, initialResult.text.length * 2.5), // Increased for DeepSeek-R1
            temperature: modelType === 'deepseek-r1' ? 0.95 : 0.9, // Higher for DeepSeek-R1 reasoning
            topP: modelType === 'deepseek-r1' ? 0.95 : 0.92,
            modelType: modelType,
            targetDetection: targetDetection,
            enableDeepThink: modelType === 'deepseek-r1'
        });

        if (refinementResult.success) {
            const refinementTime = Date.now() - startTime;
            console.log(`Multi-pass refinement completed in ${refinementTime}ms`);

            return {
                ...refinementResult,
                multiPass: true,
                refinementTime: refinementTime,
                aiAnalysis: aiAnalysis,
                originalResult: initialResult.text
            };
        } else {
            console.warn('Refinement pass failed, returning initial result');
            return initialResult;
        }

    } catch (error) {
        console.error('Multi-pass refinement error:', error.message);
        return initialResult; // Return original result if refinement fails
    }
}

/**
 * Analyze text for remaining AI detection patterns
 */
function analyzeRemainingAIPatterns(text) {
    const patterns = {
        formalTransitions: /\b(furthermore|moreover|consequently|therefore|thus|hence|additionally|similarly)\b/gi,
        roboticPhrases: /\b(it is important to note|it should be noted|it is worth mentioning|it must be emphasized)\b/gi,
        mechanicalConclusions: /\b(in conclusion|to summarize|in summary|to conclude|finally)\b/gi,
        overqualification: /\b(comprehensive|extensive|significant|substantial|considerable|numerous|various)\b/gi,
        passiveOveruse: /\b(is|are|was|were|been|being)\s+\w+ed\b/gi,
        repetitiveStructure: /^[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]\s+[A-Z][^.!?]*[.!?]/gm
    };

    let riskScore = 0;
    const issues = [];

    Object.entries(patterns).forEach(([category, pattern]) => {
        const matches = text.match(pattern) || [];
        if (matches.length > 0) {
            riskScore += matches.length;
            issues.push({
                category,
                count: matches.length,
                examples: matches.slice(0, 3)
            });
        }
    });

    // Additional checks
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    const avgSentenceLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;

    if (avgSentenceLength > 25) {
        riskScore += 2;
        issues.push({ category: 'longSentences', count: 1, examples: ['Average sentence length too high'] });
    }

    return {
        riskScore: Math.min(riskScore, 10),
        issues,
        recommendations: generateRefinementRecommendations(issues)
    };
}

/**
 * Generate targeted refinement prompt based on AI pattern analysis
 */
function generateRefinementPrompt(text, aiAnalysis, options) {
    const { targetDetection, aggressiveness, originalText } = options;

    const issueDescriptions = aiAnalysis.issues.map(issue =>
        `- ${issue.category}: ${issue.count} instances (e.g., "${issue.examples[0]}")`
    ).join('\n');

    return `You are performing a CRITICAL REFINEMENT PASS to eliminate remaining AI detection patterns. The text below has already been humanized but still contains ${aiAnalysis.riskScore}/10 AI risk factors.

🎯 REFINEMENT MISSION: Achieve ≤${targetDetection}% AI detection by targeting these specific issues:

${issueDescriptions}

🔧 TARGETED REFINEMENT STRATEGIES:
1. ELIMINATE FORMAL TRANSITIONS: Replace with natural thought connectors
2. BREAK REPETITIVE PATTERNS: Vary sentence structures dramatically
3. INJECT HUMAN SPONTANEITY: Add natural digressions and personal touches
4. REDUCE PASSIVE VOICE: Convert to active, engaging language
5. ADD CONVERSATIONAL ELEMENTS: Include natural speech patterns
6. VARY SENTENCE RHYTHM: Mix short punchy sentences with flowing longer ones

🚫 CRITICAL ELIMINATION TARGETS:
- All instances of "furthermore", "moreover", "consequently"
- Robotic phrases like "it is important to note"
- Mechanical conclusions and summaries
- Overuse of qualifying adjectives
- Predictable sentence patterns

✅ PRESERVE COMPLETELY:
- Core message and factual content
- Technical accuracy and data
- Professional credibility
- Logical flow and structure

TEXT TO REFINE:
${text}

ULTRA-HUMANIZED REFINEMENT (≤${targetDetection}% AI detection):`;
}

/**
 * Generate specific recommendations for refinement
 */
function generateRefinementRecommendations(issues) {
    const recommendations = [];

    issues.forEach(issue => {
        switch (issue.category) {
            case 'formalTransitions':
                recommendations.push('Replace formal transitions with conversational connectors');
                break;
            case 'roboticPhrases':
                recommendations.push('Eliminate robotic qualifying phrases');
                break;
            case 'mechanicalConclusions':
                recommendations.push('Use natural ending patterns instead of formal conclusions');
                break;
            case 'overqualification':
                recommendations.push('Reduce excessive qualifying adjectives');
                break;
            case 'passiveOveruse':
                recommendations.push('Convert passive voice to active constructions');
                break;
            case 'repetitiveStructure':
                recommendations.push('Vary sentence structures and lengths');
                break;
            case 'longSentences':
                recommendations.push('Break up overly long sentences');
                break;
        }
    });

    return recommendations;
}

/**
 * Enhanced validation with more sophisticated AI pattern detection
 */
async function validateDetectionTarget(text, targetDetection) {
    // Enhanced heuristic validation - in production, this would call an AI detection API

    const aiPatterns = [
        { pattern: /\b(furthermore|moreover|consequently|therefore|thus|hence)\b/gi, weight: 3 },
        { pattern: /\b(it is important to note|it should be noted|it is worth mentioning)\b/gi, weight: 4 },
        { pattern: /\b(in conclusion|to summarize|in summary)\b/gi, weight: 3 },
        { pattern: /\b(comprehensive|extensive|significant|substantial|considerable)\b/gi, weight: 2 },
        { pattern: /\b(numerous|various|multiple|several)\s+\w+/gi, weight: 1 },
        { pattern: /\b(is|are|was|were)\s+\w+ed\b/gi, weight: 1 }
    ];

    let weightedScore = 0;
    let totalMatches = 0;

    aiPatterns.forEach(({ pattern, weight }) => {
        const matches = text.match(pattern) || [];
        weightedScore += matches.length * weight;
        totalMatches += matches.length;
    });

    // Calculate estimated detection percentage
    const textLength = text.length;
    const density = (weightedScore / textLength) * 1000; // Patterns per 1000 characters
    const estimatedDetection = Math.min(density * 8, 95); // Adjusted multiplier

    console.log(`Detection validation: ${totalMatches} patterns, density: ${density.toFixed(2)}, estimated: ${estimatedDetection.toFixed(1)}%`);

    return estimatedDetection <= targetDetection;
}

/**
 * Check if advanced LLM service is available
 */
export function isAdvancedLLMAvailable() {
    // Check if at least one API key is configured
    const availableKeys = [
        'FIREWORKS_API_KEY',
        'NOVITA_API_KEY', 
        'OPENROUTER_API_KEY',
        'GROQ_API_KEY'
    ].filter(key => process.env[key]);
    
    return availableKeys.length > 0;
}

/**
 * Get status of available providers
 */
export function getProviderStatus() {
    const status = {};
    
    Object.entries(MODEL_CONFIGS).forEach(([modelName, config]) => {
        status[modelName] = config.providers.map(provider => ({
            name: provider.name,
            available: !!process.env[provider.apiKeyEnv],
            model: provider.model
        }));
    });
    
    return status;
}
